{"version": 3, "file": "custom-nav.js", "sources": ["components/custom-nav/custom-nav.vue", "../../../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovd29yay9naXREYXRhL251b2R1bi1ib290L251b2R1bi11aS9udW9kdW4tYWRtaW4tYXBwL2NvbXBvbmVudHMvY3VzdG9tLW5hdi9jdXN0b20tbmF2LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view>\n\t\t<view class=\"custom-nav\"\n\t\t\t:class=\"{ 'nav-transparent': transparent, 'nav-hidden': hidden }\"\n\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px' }\">\n\t\t\t<view class=\"nav-content\">\n\t\t\t\t<view class=\"left-area\">\n\t\t\t\t\t<slot name=\"left\" v-if=\"showLeft\">\n\t\t\t\t\t\t<view class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t\t\t\t<uni-icons type=\"back\" size=\"20\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</slot>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"title-area\" v-if=\"showTitle\">\n\t\t\t\t\t<slot name=\"title\">\n\t\t\t\t\t\t<text class=\"title\">{{ title }}</text>\n\t\t\t\t\t</slot>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"right-area\">\n\t\t\t\t\t<slot name=\"right\" v-if=\"showRight\"></slot>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view :style=\"{ height: totalHeight + 'px' }\"></view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\n\n// 定义props\nconst props = defineProps({\n\ttitle: {\n\t\ttype: String,\n\t\tdefault: ''\n\t},\n\tshowLeft: {\n\t\ttype: Boolean,\n\t\tdefault: false\n\t},\n\tshowRight: {\n\t\ttype: Boolean,\n\t\tdefault: false\n\t},\n\tshowTitle: {\n\t\ttype: Boolean,\n\t\tdefault: true\n\t},\n\ttransparent: {\n\t\ttype: Boolean,\n\t\tdefault: false\n\t},\n\thidden: {\n\t\ttype: Boolean,\n\t\tdefault: false\n\t},\n\tpath: {\n\t\ttype: String,\n\t\tdefault: '/'\n\t}\n})\n\n// 响应式数据\nconst statusBarHeight = ref(20)\n\n// 计算属性\nconst totalHeight = computed(() => {\n\treturn statusBarHeight.value + 44\n})\n\n// 生命周期钩子\nonMounted(() => {\n\t// 获取状态栏高度\n\tconst systemInfo = uni.getSystemInfoSync()\n\tstatusBarHeight.value = systemInfo.statusBarHeight\n})\n\n// 方法\nconst goBack = () => {\n\tconst pages = getCurrentPages()\n\tif (pages.length > 1) {\n\t\t// 使用 delta 参数返回上一页，不刷新\n\t\tuni.navigateBack({\n\t\t\tdelta: 1,\n\t\t})\n\t} else if (props.path) {\n\t\tuni.redirectTo({\n\t\t\turl: props.path\n\t\t})\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.custom-nav {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tz-index: 999;\n\tbackground-color: #ffffff;\n\n\t.nav-content {\n\t\theight: 44px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 15px;\n\t\tposition: relative;\n\n\t\t.left-area {\n\t\t\twidth: 60px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tposition: absolute;\n\t\t\tleft: 15px;\n\t\t\tz-index: 1;\n\n\t\t\t.back-btn {\n\t\t\t\tpadding: 5px;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.icon-back {\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\tfont-size: 20px;\n\t\t\t\t\tline-height: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.title-area {\n\t\t\tposition: absolute;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tpointer-events: none;\n\n\t\t\t.title {\n\t\t\t\tcolor: #333333;\n\t\t\t\tfont-size: 17px;\n\t\t\t\t// font-weight: 500;\n\t\t\t\tline-height: 44px;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\n\t\t.right-area {\n\t\t\twidth: 60px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: flex-end;\n\t\t\tposition: absolute;\n\t\t\tright: 15px;\n\t\t\tz-index: 1;\n\t\t}\n\t}\n\n\t// 透明模式\n\t&.nav-transparent {\n\t\tbackground-color: transparent;\n\t}\n\n\t// 隐藏模式\n\t&.nav-hidden {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n}\n</style>", "import Component from 'D:/work/gitData/nuodun-boot/nuodun-ui/nuodun-admin-app/components/custom-nav/custom-nav.vue'\nwx.createComponent(Component)"], "names": ["props", "__props", "statusBarHeight", "ref", "totalHeight", "computed", "value", "onMounted", "systemInfo", "uni", "index", "getSystemInfoSync", "goBack", "getCurrentPages", "length", "navigateBack", "delta", "path", "redirectTo", "url", "wx", "createComponent", "Component"], "mappings": "uYA+BA,MAAMA,EAAQC,EAgCRC,EAAkBC,EAAGA,IAAC,IAGtBC,EAAcC,EAAQA,UAAC,IACrBH,EAAgBI,MAAQ,KAIhCC,EAAAA,WAAU,KAEH,MAAAC,EAAaC,EAAGC,MAACC,oBACvBT,EAAgBI,MAAQE,EAAWN,mBAIpC,MAAMU,EAAS,KACAC,kBACJC,OAAS,EAElBL,EAAAA,MAAIM,aAAa,CAChBC,MAAO,IAEEhB,EAAMiB,MAChBR,EAAAA,MAAIS,WAAW,CACdC,IAAKnB,EAAMiB,8UCtFdG,GAAGC,gBAAgBC"}