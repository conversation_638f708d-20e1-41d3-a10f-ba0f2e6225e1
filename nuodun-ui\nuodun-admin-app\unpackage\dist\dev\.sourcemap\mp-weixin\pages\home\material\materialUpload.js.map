{"version": 3, "file": "materialUpload.js", "sources": ["pages/home/<USER>/materialUpload.vue", "../../../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNcaG9tZVxtYXRlcmlhbFxtYXRlcmlhbFVwbG9hZC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <custom-nav title=\"材料上传\" :showLeft=\"true\"></custom-nav>\r\n\r\n    <!-- 加载状态 -->\r\n    <view v-if=\"loading\" class=\"loading-wrapper\">\r\n      <uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\r\n    </view>\r\n\r\n    <!-- 页面内容 -->\r\n    <view v-else class=\"material-upload\">\r\n      <!-- 学生在读类型选择 -->\r\n      <view v-if=\"showStudentStatusSelect\" class=\"select-section\">\r\n        <view class=\"section-title\">请选择学生在读类型</view>\r\n        <text class=\"select-tip\">请先选择该学生的在读类型，以便为您展示对应的材料配置：</text>\r\n        <view class=\"picker-wrapper\">\r\n          <uni-data-picker placeholder=\"请选择学生在读类型\" popup-title=\"请选择学生在读类型\"\r\n                           :localdata=\"studentStatusOptions\" v-model=\"formData.studentStatus\"></uni-data-picker>\r\n        </view>\r\n        <view class=\"select-actions\">\r\n          <button class=\"action-btn confirm-btn\" @tap=\"handleStudentStatusConfirm\">\r\n            确认\r\n          </button>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 材料列表为空 -->\r\n      <view v-else-if=\"!materialOptList.length\" class=\"empty-section\">\r\n        <view class=\"empty-icon\">📋</view>\r\n        <text class=\"empty-text\">暂无可上传的材料配置</text>\r\n      </view>\r\n\r\n      <!-- 材料列表 -->\r\n      <view v-else class=\"materials-container\">\r\n        <view v-for=\"item in materialOptList\" :key=\"item.id\" class=\"material-item\"\r\n              :class=\"{ 'required': item.isRequired === 'Y' }\">\r\n\r\n          <!-- 材料头部信息 -->\r\n          <view class=\"material-header\">\r\n            <view class=\"header-left\">\r\n              <text class=\"material-title\">{{ item.materialType }}</text>\r\n              <view class=\"material-tags\">\r\n                <text class=\"type-tag\" :class=\"item.dataType === '1' ? 'file-tag' : 'text-tag'\">\r\n                  {{ item.dataType === '1' ? '文件' : '文本' }}\r\n                </text>\r\n                <text v-if=\"item.isRequired === 'Y'\" class=\"required-tag\">必填</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <text class=\"material-desc\">{{ item.description }}</text>\r\n\r\n          <!-- 材料内容 -->\r\n          <view class=\"material-content\">\r\n            <!-- 文件类型 -->\r\n            <view v-if=\"item.dataType === '1'\" class=\"file-container\">\r\n              <!-- 已上传文件列表 - 只有当有有效文件时才显示 -->\r\n              <view v-if=\"getValidFileList(item.fileList).length > 0\" class=\"file-list\">\r\n                <DocumentPreview\r\n                    v-for=\"(file, fileIndex) in getValidFileList(item.fileList)\"\r\n                    :key=\"fileIndex\"\r\n                    :file-path=\"(fileUrl + file.filePath) || file.fileFullPath || ''\"\r\n                    :file-name=\"file.sourceFileName || file.fileName || file.name || '未知文件'\"\r\n                    :allow-view=\"!!((fileUrl + file.filePath) || file.fileFullPath)\"\r\n                    :allow-download=\"!!((fileUrl + file.filePath) || file.fileFullPath)\"\r\n                    :allow-delete=\"true\"\r\n                    @delete=\"handleFileDelete(item.id, fileIndex, $event)\"\r\n                    class=\"material-file-item\"\r\n                />\r\n              </view>\r\n\r\n              <!-- 上传按钮 -->\r\n              <view class=\"upload-btn\" @tap=\"chooseFiles(item.id)\">\r\n                <text class=\"upload-icon\">+</text>\r\n                <view class=\"upload-content\">\r\n                  <text class=\"upload-text\">{{\r\n                      item.fileList && item.fileList.length > 0 ? '继续添加' : '上传文件'\r\n                    }}\r\n                  </text>\r\n                  <text class=\"upload-tip\">\r\n                    {{ isWechatBrowser ? '支持文档、图片格式（微信浏览器）' : '支持文档、图片格式' }}\r\n                  </text>\r\n                  <!-- 调试信息 -->\r\n                  <text v-if=\"isWechatBrowser\" class=\"debug-info\">\r\n                    微信浏览器环境 - 可能无法正确上传\r\n                  </text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n\r\n            <!-- 文本类型 -->\r\n            <view v-else-if=\"item.dataType === '2'\" class=\"text-container\">\r\n              <view class=\"text-header\">\r\n                <text class=\"text-title\">账号信息</text>\r\n                <text class=\"add-btn\" @tap=\"addTextRow(item.id)\">\r\n                  添加\r\n                </text>\r\n              </view>\r\n\r\n              <view class=\"text-rows\">\r\n                <view v-for=\"(row, index) in item.textDataList\" :key=\"index\" class=\"text-row\">\r\n                  <input v-model=\"row.key\" @input=\"handleTextDataChange(item.id)\" placeholder=\"例：账号\"\r\n                         class=\"text-input key-input\"/>\r\n                  <input v-model=\"row.value\" @input=\"handleTextDataChange(item.id)\" placeholder=\"例：12345678\"\r\n                         class=\"text-input value-input\"/>\r\n                  <text class=\"delete-row-btn\" @tap=\"removeTextRow(item.id, index)\"\r\n                        :class=\"{ disabled: item.textDataList.length <= 1 }\">\r\n                    删除\r\n                  </text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 底部空间，防止内容被固定按钮遮挡 -->\r\n      <view class=\"bottom-spacing\"></view>\r\n\r\n      <!-- 操作按钮 -->\r\n      <view v-if=\"materialOptList.length > 0\" class=\"action-section\">\r\n        <button class=\"action-btn save-btn\" @tap=\"handleSave\" :disabled=\"saveLoading\">\r\n          <text v-if=\"saveLoading\">保存中...</text>\r\n          <text v-else>保存材料</text>\r\n        </button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {ref, computed, getCurrentInstance} from 'vue'\r\nimport config from '@/config/environment'\r\nimport {onLoad} from '@dcloudio/uni-app'\r\nimport {getMaterialByTypeAndStatus} from '@/api/home/<USER>'\r\nimport {getMaterialByStId, saveOrUpdateStMaterial} from '@/api/home/<USER>'\r\nimport {selectStMaterialStAndType, uploadMaterialList} from '@/api/home/<USER>'\r\nimport {chooseAndUploadFile, getFileExtension, validateMaterialFileType} from '@/common/fileUtils.js'\r\nimport {createModal} from '@/common/modal'\r\nimport {validateRequiredMaterials, buildSubmitData} from './materialValidation.js'\r\nimport CustomNav from '@/components/custom-nav/custom-nav.vue'\r\nimport DocumentPreview from '@/components/document-preview/index.vue'\r\n\r\nconst fileUrl = config.fileUrl || '';\r\n// 获取 toast 实例\r\nconst {proxy} = getCurrentInstance()\r\nconst toast = proxy.toast\r\n// 字典数据\r\nconst {t_st_study_status} = proxy.useDict('t_st_study_status')\r\n\r\n// 响应式数据\r\nconst loading = ref(false)\r\nconst saveLoading = ref(false)\r\n\r\n// 加载中文本\r\nconst loadingText = {\r\n  contentdown: '正在加载...',\r\n  contentrefresh: '加载中...',\r\n  contentnomore: '没有更多数据了'\r\n}\r\n\r\n// 学生信息\r\nconst stInfo = ref({\r\n  stId: null,\r\n  stDecaType: '',\r\n  studentStatus: '',\r\n  materialPlanId: null\r\n})\r\n\r\n// 学生在读类型选择相关\r\nconst showStudentStatusSelect = ref(false)\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n  studentStatus: ''\r\n})\r\n\r\n// 学生在读类型选项数据\r\nconst studentStatusOptions = ref([])\r\n\r\n// 材料配置列表\r\nconst materialOptList = ref([])\r\n\r\n\r\n// 材料文件变更记录\r\nconst materialChanges = ref({})\r\n\r\n\r\n// 检测是否在微信浏览器中\r\nconst isWechatBrowser = computed(() => {\r\n  // #ifdef H5\r\n  if (typeof navigator !== 'undefined') {\r\n    return /micromessenger/i.test(navigator.userAgent)\r\n  }\r\n  // #endif\r\n  return false\r\n})\r\n\r\n\r\n// 获取有效的文件列表\r\nconst getValidFileList = (fileList) => {\r\n  if (!fileList || !Array.isArray(fileList)) return []\r\n  const validFiles = fileList.filter(file => file && (file.sourceFileName || file.fileName || file.name))\r\n  return validFiles\r\n}\r\n\r\n// 显示文件来源选择器（微信小程序专用）\r\nconst showFileSourceSelector = () => {\r\n  return new Promise((resolve) => {\r\n    uni.showActionSheet({\r\n      itemList: ['从手机相册选择', '从微信聊天记录选择'],\r\n      success: (res) => {\r\n        if (res.tapIndex === 0) {\r\n          resolve('album') // 从相册选择\r\n        } else if (res.tapIndex === 1) {\r\n          resolve('chat') // 从聊天记录选择\r\n        }\r\n      },\r\n      fail: () => {\r\n        resolve(null) // 用户取消\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\n// 处理文件删除\r\nconst handleFileDelete = (materialOptId, fileIndex, event) => {\r\n  // 由于我们使用了过滤后的文件列表，需要找到原始文件列表中的真实索引\r\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\r\n  if (!materialOpt || !materialOpt.fileList) return\r\n\r\n  const validFiles = getValidFileList(materialOpt.fileList)\r\n  const fileToDelete = validFiles[fileIndex]\r\n\r\n  // 在原始文件列表中找到这个文件的真实索引\r\n  const realIndex = materialOpt.fileList.findIndex(file =>\r\n      file === fileToDelete ||\r\n      (file.sourceFileName === fileToDelete.sourceFileName && file.fileFullPath === fileToDelete.fileFullPath) ||\r\n      (file.fileName === fileToDelete.fileName && file.fileUrl === fileToDelete.fileUrl) ||\r\n      (file.sourceFileName === event.fileName && file.fileFullPath === event.filePath)\r\n  )\r\n\r\n  if (realIndex !== -1) {\r\n    removeFile(materialOptId, realIndex)\r\n  } else {\r\n    console.warn('未找到要删除的文件')\r\n  }\r\n}\r\n\r\n// 处理学生在读类型确认\r\nconst handleStudentStatusConfirm = async () => {\r\n  if (!formData.value.studentStatus) {\r\n    toast.show('请选择学生在读类型')\r\n    return\r\n  }\r\n\r\n  try {\r\n    uni.showLoading('提交中...')\r\n\r\n    // 保存学生资料信息\r\n    const stMaterialData = {\r\n      stId: stInfo.value.stId,\r\n      stDecaType: stInfo.value.stDecaType,\r\n      studentStatus: formData.value.studentStatus,\r\n      stFileCreStatus: '1' // 初始状态待上传\r\n    }\r\n\r\n    await saveOrUpdateStMaterial(stMaterialData)\r\n\r\n    // 更新学生信息\r\n    stInfo.value.studentStatus = formData.value.studentStatus\r\n\r\n    // 隐藏选择界面，加载材料配置\r\n    showStudentStatusSelect.value = false\r\n    await loadData()\r\n\r\n    uni.hideLoading()\r\n    toast.show('学生在读类型设置成功')\r\n  } catch (error) {\r\n    console.error('保存学生在读类型失败:', error)\r\n    uni.hideLoading()\r\n  }\r\n}\r\n\r\n// 添加文本行\r\nconst addTextRow = (materialOptId) => {\r\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\r\n  if (!materialOpt) return\r\n\r\n  if (!materialOpt.textDataList) {\r\n    materialOpt.textDataList = []\r\n  }\r\n\r\n  materialOpt.textDataList.push({key: '', value: ''})\r\n  handleTextDataChange(materialOptId)\r\n}\r\n\r\n// 删除文本行\r\nconst removeTextRow = (materialOptId, index) => {\r\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\r\n  if (!materialOpt || !materialOpt.textDataList) return\r\n\r\n  // 至少保留一行\r\n  if (materialOpt.textDataList.length <= 1) return\r\n\r\n  materialOpt.textDataList.splice(index, 1)\r\n  handleTextDataChange(materialOptId)\r\n}\r\n\r\n// 文本数据变更处理\r\nconst handleTextDataChange = (materialOptId) => {\r\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\r\n  if (!materialOpt) return\r\n\r\n  // 更新变更记录\r\n  if (!materialChanges.value[materialOptId]) {\r\n    materialChanges.value[materialOptId] = {\r\n      id: materialOpt.planFileId || null,\r\n      materialOptId: materialOptId,\r\n      materialType: materialOpt.materialType,\r\n      description: materialOpt.description,\r\n      planType: materialOpt.dataType,\r\n      fileList: [],\r\n      textDataList: [{key: '', value: ''}],\r\n      isModified: false\r\n    }\r\n  }\r\n\r\n  materialChanges.value[materialOptId].textDataList = [...(materialOpt.textDataList || [])]\r\n  materialChanges.value[materialOptId].isModified = true\r\n}\r\n\r\n// 选择文件\r\nconst chooseFiles = async (materialOptId) => {\r\n  try {\r\n    // 在微信浏览器中显示特殊提示和调试信息\r\n    if (isWechatBrowser.value) {\r\n    } else {\r\n    }\r\n\r\n    // #ifdef MP-WEIXIN\r\n    // 微信小程序环境，先让用户选择文件来源\r\n    const sourceType = await showFileSourceSelector()\r\n    if (!sourceType) {\r\n      return // 用户取消选择\r\n    }\r\n    // #endif\r\n\r\n    const result = await chooseAndUploadFile({\r\n      count: 10, // 最多选择10个文件\r\n      maxSize: 10, // 10MB限制\r\n      allowedTypes: ['image', 'document'], // 支持图片、文档、视频和其他文件类型\r\n      acceptTypes: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,.csv,image/*', // H5环境下精确指定文件类型\r\n      // #ifdef MP-WEIXIN\r\n      fileSourceType: sourceType, // 传递文件来源类型\r\n      // #endif\r\n      uploadData: {\r\n        fileBizType: 'material_upload',\r\n        materialOptId: materialOptId\r\n      },\r\n      onProgress: (progress) => {\r\n      },\r\n      onSuccess: (uploadResults) => {\r\n      }\r\n    })\r\n\r\n    if (result.uploaded && result.uploadResults) {\r\n      // 上传成功，更新文件列表\r\n      const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\r\n      if (materialOpt) {\r\n        if (!materialOpt.fileList) {\r\n          materialOpt.fileList = []\r\n        }\r\n\r\n        // 验证并添加新上传的文件到列表\r\n        const validFiles = []\r\n        const invalidFiles = []\r\n\r\n        result.uploadResults.forEach(file => {\r\n          // 使用原始文件名进行验证\r\n          const sourceFileName = file.sourceFileName || file.name\r\n          const filePath = file.filePath\r\n\r\n          // 使用增强的文件类型验证，支持从 filePath 获取扩展名\r\n          if (validateMaterialFileType(sourceFileName, filePath)) {\r\n            // 保持原始数据结构，不修改任何字段名\r\n            validFiles.push({\r\n              ...file, // 保持所有原始字段\r\n              // 确保有必要的字段用于显示\r\n              displayName: sourceFileName || file.fileName || file.name || '未知文件'\r\n            })\r\n          } else {\r\n            invalidFiles.push(sourceFileName || filePath || '未知文件')\r\n          }\r\n        })\r\n\r\n        if (validFiles.length > 0) {\r\n          materialOpt.fileList.push(...validFiles)\r\n          // 更新变更记录\r\n          updateMaterialChange(materialOptId, 'fileList', materialOpt.fileList)\r\n          console.log('文件列表已更新:', materialOpt.fileList)\r\n        }\r\n\r\n        // 显示结果提示\r\n        if (invalidFiles.length > 0) {\r\n          toast.show(`${invalidFiles.length}个文件格式不支持`)\r\n        } else {\r\n          toast.show('文件上传成功')\r\n        }\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('文件选择或上传失败:', error)\r\n  }\r\n}\r\n\r\n// 删除文件\r\nconst removeFile = (materialOptId, fileIndex) => {\r\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\r\n  if (!materialOpt || !materialOpt.fileList) return\r\n\r\n  materialOpt.fileList.splice(fileIndex, 1)\r\n  updateMaterialChange(materialOptId, 'fileList', materialOpt.fileList)\r\n\r\n  toast.show('文件已删除')\r\n}\r\n\r\n// 更新材料变更记录\r\nconst updateMaterialChange = (materialOptId, type, data) => {\r\n  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)\r\n  if (!materialOpt) return\r\n\r\n  if (!materialChanges.value[materialOptId]) {\r\n    materialChanges.value[materialOptId] = {\r\n      id: materialOpt.planFileId || null,\r\n      materialOptId: materialOptId,\r\n      materialType: materialOpt.materialType,\r\n      description: materialOpt.description,\r\n      planType: materialOpt.dataType,\r\n      fileList: [],\r\n      textDataList: [{key: '', value: ''}],\r\n      isModified: false\r\n    }\r\n  }\r\n\r\n  if (type === 'fileList') {\r\n    materialChanges.value[materialOptId].fileList = [...(data || [])]\r\n  } else if (type === 'textDataList') {\r\n    materialChanges.value[materialOptId].textDataList = [...(data || [])]\r\n  }\r\n\r\n  materialChanges.value[materialOptId].isModified = true\r\n}\r\n\r\n// 验证必填材料（使用工具类）\r\nconst validateMaterials = () => {\r\n  return validateRequiredMaterials(materialOptList.value, materialChanges.value, toast)\r\n}\r\n\r\n// 提交材料数据\r\nconst submitAgreement = async () => {\r\n  try {\r\n    uni.showLoading('保存中...')\r\n    saveLoading.value = true\r\n\r\n    // 构建提交数据 - 使用工具类\r\n    const submitData = buildSubmitData(materialChanges.value)\r\n\r\n    if (submitData.length === 0) {\r\n      toast.show('没有检测到文件变更')\r\n      return\r\n    }\r\n\r\n    // 构建请求参数\r\n    const param = {\r\n      stId: stInfo.value.stId,\r\n      stDecaType: stInfo.value.stDecaType,\r\n      materialPlanId: stInfo.value.materialPlanId,\r\n      fileOptType: '1',\r\n      materialPlanFileList: submitData\r\n    }\r\n\r\n    // 调用批量提交接口\r\n    await uploadMaterialList(param)\r\n\r\n    toast.show('保存成功')\r\n\r\n    // 延迟返回上一页\r\n    setTimeout(() => {\r\n      uni.navigateBack()\r\n    }, 500)\r\n\r\n  } catch (error) {\r\n    console.error('保存失败:', error)\r\n  } finally {\r\n    uni.hideLoading();\r\n    saveLoading.value = false\r\n  }\r\n}\r\n\r\n// 保存材料\r\nconst handleSave = async () => {\r\n  // 验证必填材料\r\n  if (!validateMaterials()) {\r\n    return\r\n  }\r\n\r\n  // 显示确认对话框\r\n  try {\r\n    const result = await createModal({\r\n      title: '确认提交',\r\n      content: '确定要提交该协议吗？提交后将不可修改',\r\n      confirmText: '确定',\r\n      cancelText: '取消'\r\n    })\r\n\r\n    if (result.confirm) {\r\n      await submitAgreement()\r\n    }\r\n  } catch (error) {\r\n    console.error('确认对话框错误:', error)\r\n    // 如果对话框出错，直接提交\r\n    await submitAgreement()\r\n  }\r\n}\r\n\r\n// 加载材料配置和已上传文件\r\nconst loadData = async () => {\r\n  if (!stInfo.value.stId) return\r\n\r\n  loading.value = true\r\n  try {\r\n    // 根据学生类型和在读状态查询材料配置\r\n    const [optRes, fileRes] = await Promise.all([\r\n      getMaterialByTypeAndStatus({\r\n        stDecaType: stInfo.value.stDecaType,\r\n        studentStatus: stInfo.value.studentStatus\r\n      }),\r\n      selectStMaterialStAndType({\r\n        stId: stInfo.value.stId,\r\n        fileOptType: '1',\r\n        materialPlanId: stInfo.value.materialPlanId\r\n      })\r\n    ])\r\n\r\n    // 获取材料配置列表\r\n    const materialOpts = optRes.data || []\r\n\r\n    // 获取已上传文件列表\r\n    const uploadedFiles = fileRes.data || []\r\n\r\n    // 创建已上传文件的映射\r\n    const uploadedFileMap = {}\r\n    uploadedFiles.forEach(file => {\r\n      if (!uploadedFileMap[file.materialOptId]) {\r\n        uploadedFileMap[file.materialOptId] = []\r\n      }\r\n\r\n      // 解析 dataJson 字段\r\n      let fileList = []\r\n      if (file.dataJson) {\r\n        try {\r\n          if (typeof file.dataJson === 'string') {\r\n            fileList = JSON.parse(file.dataJson)\r\n          } else {\r\n            fileList = file.dataJson\r\n          }\r\n        } catch (e) {\r\n          console.warn('解析文件JSON失败:', e)\r\n          fileList = []\r\n        }\r\n      }\r\n\r\n      // 将文件信息添加到映射中\r\n      if (Array.isArray(fileList) && fileList.length > 0) {\r\n        uploadedFileMap[file.materialOptId].push({\r\n          planFileId: file.id,\r\n          fileList: fileList\r\n        })\r\n      }\r\n    })\r\n\r\n    // 合并配置数据和已上传文件数据\r\n    materialOptList.value = materialOpts.map(opt => {\r\n      const uploadedData = uploadedFileMap[opt.id]\r\n      let fileList = []\r\n      let textDataList = [{key: '', value: ''}]\r\n      let planFileId = null\r\n      let dataType = opt.dataType || '1'\r\n\r\n      if (uploadedData && uploadedData.length > 0) {\r\n        const firstData = uploadedData[0]\r\n        planFileId = firstData.planFileId\r\n\r\n        const dbRecord = uploadedFiles.find(f => f.id === firstData.planFileId)\r\n        if (dbRecord) {\r\n          if (dataType === '2') {\r\n            // 文本类型，解析文本数据\r\n            try {\r\n              if (dbRecord.dataJson) {\r\n                const parsedData = typeof dbRecord.dataJson === 'string'\r\n                    ? JSON.parse(dbRecord.dataJson)\r\n                    : dbRecord.dataJson\r\n\r\n                if (Array.isArray(parsedData)) {\r\n                  textDataList = parsedData.length > 0 ? parsedData : [{key: '', value: ''}]\r\n                } else if (parsedData && typeof parsedData === 'object') {\r\n                  textDataList = [parsedData]\r\n                } else {\r\n                  textDataList = [{key: '', value: ''}]\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.warn('解析文本数据失败:', e)\r\n              textDataList = [{key: '', value: ''}]\r\n            }\r\n          } else {\r\n            // 文件类型，合并所有已上传的文件\r\n            uploadedData.forEach(data => {\r\n              fileList = fileList.concat(data.fileList || [])\r\n            })\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        ...opt,\r\n        fileList: fileList,\r\n        textDataList: textDataList,\r\n        dataType: dataType,\r\n        planFileId: planFileId\r\n      }\r\n    })\r\n\r\n    // 初始化变更记录\r\n    materialOptList.value.forEach(item => {\r\n      materialChanges.value[item.id] = {\r\n        id: item.planFileId || null,\r\n        materialOptId: item.id,\r\n        materialType: item.materialType,\r\n        description: item.description,\r\n        planType: item.dataType || '1',\r\n        fileList: item.fileList || [],\r\n        textDataList: item.textDataList || [{key: '', value: ''}],\r\n        isModified: false\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error('加载数据失败:', error)\r\n    toast.show('加载数据失败')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack()\r\n}\r\n\r\n// 初始化学生在读类型选项数据\r\nconst initStudentStatusOptions = () => {\r\n  // 将字典数据转换为uni-data-picker需要的格式\r\n  studentStatusOptions.value = t_st_study_status.value.map(item => ({\r\n    text: item.label,\r\n    value: item.value\r\n  }))\r\n  console.log('学生在读类型选项:', studentStatusOptions.value)\r\n}\r\n\r\n// 页面初始化\r\nconst initPage = async (options = {}) => {\r\n  // 从页面参数获取学生信息\r\n  stInfo.value = {\r\n    stId: parseInt(options.stId) || null,\r\n  }\r\n\r\n  // 初始化学生在读类型选项数据\r\n  initStudentStatusOptions()\r\n\r\n  // 检查学生在读类型\r\n  if (!stInfo.value.studentStatus) {\r\n    // 先查询学生资料信息\r\n    try {\r\n      const res = await getMaterialByStId(stInfo.value.stId)\r\n      if (res.data && res.data.studentStatus) {\r\n        stInfo.value.studentStatus = res.data.studentStatus\r\n        stInfo.value.stDecaType = res.data.stDecaType\r\n        // 有在读类型，直接加载数据\r\n        loadData()\r\n      } else {\r\n        // 没有在读类型，显示选择界面\r\n        showStudentStatusSelect.value = true\r\n      }\r\n    } catch (error) {\r\n      console.error('获取学生资料信息失败:', error)\r\n      // 如果查询失败，也显示选择界面\r\n      showStudentStatusSelect.value = true\r\n    }\r\n  } else {\r\n    // 已有在读类型，直接加载数据\r\n    loadData()\r\n  }\r\n}\r\n\r\n// 使用 onLoad 获取页面参数\r\nonLoad((options) => {\r\n  initPage(options)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  padding: 0;\r\n}\r\n\r\n.loading-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 300rpx;\r\n}\r\n\r\n.material-upload {\r\n  padding: 30rpx;\r\n}\r\n\r\n// 选择学生在读类型区域\r\n.select-section {\r\n  background-color: #fff;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 8rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.select-tip {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  line-height: 1.6;\r\n  margin-bottom: 30rpx;\r\n  display: block;\r\n}\r\n\r\n// uni-data-picker 样式\r\n.picker-wrapper {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n:deep(.uni-data-tree) {\r\n  padding: 10rpx 0;\r\n}\r\n\r\n:deep(.uni-data-pickerview__item) {\r\n  line-height: 80rpx;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n:deep(.uni-data-picker__input-text) {\r\n  font-size: 28rpx;\r\n}\r\n\r\n:deep(.input-value) {\r\n  padding: 24rpx 32rpx;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8rpx;\r\n  border: 2rpx solid #e0e0e0;\r\n  transition: border-color 0.3s ease;\r\n  min-height: 60rpx;\r\n\r\n  &:active {\r\n    border-color: var(--nuodun-primary-color);\r\n  }\r\n}\r\n\r\n.select-actions {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-around;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.action-btn {\r\n  width: 45%;\r\n  height: 88rpx;\r\n  line-height: 88rpx;\r\n  font-size: 28rpx;\r\n  border-radius: 44rpx;\r\n  margin: 0 10rpx;\r\n  border: none;\r\n  text-align: center;\r\n}\r\n\r\n.confirm-btn {\r\n  background: linear-gradient(135deg, var(--nuodun-primary-color, #1890ff) 0%, var(--nuodun-primary-color-light, #4dabff) 100%);\r\n  color: #fff;\r\n  box-shadow: 0 4rpx 12rpx rgba(var(--nuodun-primary-color-rgb, 24, 144, 255), 0.3);\r\n}\r\n\r\n.reject-btn {\r\n  background-color: #fff;\r\n  color: #ff3b30;\r\n  border: 1px solid #ff3b30;\r\n}\r\n\r\n// 空状态\r\n.empty-section {\r\n  background-color: #fff;\r\n  padding: 60rpx 30rpx;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 8rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 100rpx;\r\n  margin-bottom: 20rpx;\r\n  opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n// 材料容器 - 整体卡片\r\n.materials-container {\r\n  background-color: #fff;\r\n  border-radius: 8rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 20rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n// 材料项\r\n.material-item {\r\n  padding: 24rpx 30rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  position: relative;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  &.required {\r\n    border-left: 4rpx solid #ff4757;\r\n    padding-left: 26rpx;\r\n  }\r\n}\r\n\r\n.material-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.material-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.material-tags {\r\n  display: flex;\r\n  gap: 8rpx;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.type-tag,\r\n.required-tag {\r\n  padding: 4rpx 8rpx;\r\n  border-radius: 4rpx;\r\n  font-size: 18rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.file-tag {\r\n  background-color: #e3f2fd;\r\n  color: #1976d2;\r\n}\r\n\r\n.text-tag {\r\n  background-color: #f3e5f5;\r\n  color: #7b1fa2;\r\n}\r\n\r\n.required-tag {\r\n  background-color: #ffebee;\r\n  color: #d32f2f;\r\n}\r\n\r\n.material-desc {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  line-height: 1.4;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.material-content {\r\n  margin-top: 16rpx;\r\n}\r\n\r\n// 文件容器\r\n.file-container {\r\n  width: 100%;\r\n}\r\n\r\n// 文件列表\r\n.file-list {\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n// 材料文件项样式\r\n.material-file-item {\r\n  margin-bottom: 12rpx;\r\n\r\n  :deep(.file-card) {\r\n    margin-bottom: 0;\r\n    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);\r\n    border: 1rpx solid #f0f0f0;\r\n    background-color: #fff;\r\n  }\r\n\r\n  :deep(.file-card-btn) {\r\n    font-size: 24rpx;\r\n    padding: 6rpx 16rpx;\r\n  }\r\n\r\n  :deep(.file-card-icon) {\r\n    font-size: 32rpx;\r\n  }\r\n\r\n  :deep(.file-card-name) {\r\n    font-size: 26rpx;\r\n  }\r\n}\r\n\r\n// 上传按钮\r\n.upload-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16rpx 20rpx;\r\n  border: 1rpx dashed #ddd;\r\n  border-radius: 6rpx;\r\n  background-color: #fafafa;\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    border-color: var(--nuodun-primary-color);\r\n    background-color: rgba(24, 144, 255, 0.02);\r\n  }\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 20rpx;\r\n  color: var(--nuodun-primary-color);\r\n  margin-right: 12rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.upload-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 24rpx;\r\n  color: var(--nuodun-primary-color);\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 20rpx;\r\n  color: #999;\r\n  line-height: 1.2;\r\n}\r\n\r\n.debug-info {\r\n  font-size: 18rpx;\r\n  color: #ff6b35;\r\n  line-height: 1.2;\r\n  margin-top: 4rpx;\r\n  text-align: center;\r\n}\r\n\r\n// 文本容器\r\n.text-container {\r\n  width: 100%;\r\n}\r\n\r\n.text-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.text-title {\r\n  font-size: 24rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.add-btn {\r\n  color: var(--nuodun-primary-color);\r\n  font-size: 22rpx;\r\n  padding: 8rpx 12rpx;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n\r\n  &:active {\r\n    color: var(--nuodun-primary-dark);\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n.text-rows {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12rpx;\r\n}\r\n\r\n.text-row {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n}\r\n\r\n.text-input {\r\n  flex: 1;\r\n  height: 60rpx;\r\n  padding: 0 16rpx;\r\n  background-color: #f8f9fa;\r\n  border: 1rpx solid #e0e0e0;\r\n  border-radius: 6rpx;\r\n  font-size: 24rpx;\r\n  color: #333;\r\n  transition: all 0.3s ease;\r\n\r\n  &.key-input {\r\n    flex: 1;\r\n  }\r\n\r\n  &.value-input {\r\n    flex: 2;\r\n  }\r\n\r\n  &:focus {\r\n    border-color: var(--nuodun-primary-color);\r\n    background-color: #fff;\r\n  }\r\n\r\n  &::placeholder {\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.delete-row-btn {\r\n  color: #ff4757;\r\n  font-size: 22rpx;\r\n  padding: 8rpx 12rpx;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n\r\n  &.disabled {\r\n    color: #ccc;\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n  }\r\n\r\n  &:not(.disabled):active {\r\n    color: #ff3742;\r\n    transform: scale(0.95);\r\n  }\r\n}\r\n\r\n// 底部操作按钮 - 与agreementSign.vue保持一致\r\n.action-section {\r\n  background-color: #fff;\r\n  padding: 30rpx;\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 1000;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.action-btn {\r\n  width: 100%;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  border-radius: 45rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  border: none;\r\n  text-align: center;\r\n  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.save-btn {\r\n  background: linear-gradient(135deg, var(--nuodun-primary-color) 0%, var(--nuodun-primary-color-light, #4dabff) 100%);\r\n  color: #fff !important;\r\n\r\n  &[disabled] {\r\n    opacity: 0.6;\r\n    background: #ccc;\r\n    color: #999 !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  &:not([disabled]):active {\r\n    transform: scale(0.98);\r\n  }\r\n}\r\n\r\n.bottom-spacing {\r\n  height: 130rpx;\r\n}\r\n\r\n// 响应式设计和动画效果\r\n@media (max-width: 750rpx) {\r\n  .page-content {\r\n    padding: 20rpx;\r\n  }\r\n\r\n  .material-item {\r\n    padding: 24rpx;\r\n  }\r\n\r\n  .material-title {\r\n    font-size: 28rpx;\r\n  }\r\n\r\n  .text-row {\r\n    flex-direction: column;\r\n    gap: 12rpx;\r\n  }\r\n\r\n  .text-input {\r\n    width: 100%;\r\n\r\n    &.key-input,\r\n    &.value-input {\r\n      flex: none;\r\n    }\r\n  }\r\n\r\n  .delete-row-btn {\r\n    align-self: flex-end;\r\n    width: 50rpx;\r\n    height: 50rpx;\r\n    font-size: 28rpx;\r\n  }\r\n\r\n  .bottom-actions {\r\n    padding: 15rpx 20rpx;\r\n  }\r\n\r\n  .save-btn {\r\n    height: 76rpx;\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n\r\n// 动画效果\r\n.material-item {\r\n  animation: fadeInUp 0.3s ease-out;\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20rpx);\r\n  }\r\n\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n// 暗色模式支持\r\n@media (prefers-color-scheme: dark) {\r\n  :root {\r\n    --nuodun-bg-color: #1c1c1e;\r\n    --nuodun-card-bg: #2c2c2e;\r\n    --nuodun-text-primary: #ffffff;\r\n    --nuodun-text-secondary: #aeaeb2;\r\n    --nuodun-text-placeholder: #8e8e93;\r\n    --nuodun-border-color: #38383a;\r\n    --nuodun-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/gitData/nuodun-boot/nuodun-ui/nuodun-admin-app/pages/home/<USER>/materialUpload.vue'\nwx.createPage(MiniProgramPage)"], "names": ["CustomNav", "DocumentPreview", "fileUrl", "config", "proxy", "getCurrentInstance", "toast", "t_st_study_status", "useDict", "loading", "ref", "saveLoading", "loadingText", "contentdown", "contentrefresh", "contentnomore", "stInfo", "stId", "stDecaType", "studentStatus", "materialPlanId", "showStudentStatusSelect", "formData", "studentStatusOptions", "materialOptList", "materialChanges", "isWechatBrowser", "computed", "getValidFileList", "fileList", "Array", "isArray", "filter", "file", "sourceFileName", "fileName", "name", "handleStudentStatusConfirm", "async", "value", "common_vendor", "index", "showLoading", "stMaterialData", "stFileCreStatus", "saveOrUpdateStMaterial", "loadData", "uni", "hideLoading", "show", "error", "__f__", "handleTextDataChange", "materialOptId", "materialOpt", "find", "item", "id", "planFileId", "materialType", "description", "planType", "dataType", "textDataList", "key", "isModified", "chooseFiles", "sourceType", "Promise", "resolve", "showActionSheet", "itemList", "success", "res", "tapIndex", "fail", "result", "chooseAndUploadFile", "count", "maxSize", "allowedTypes", "acceptTypes", "fileSourceType", "uploadData", "fileBizType", "onProgress", "progress", "onSuccess", "uploadResults", "uploaded", "validFiles", "invalidFiles", "for<PERSON>ach", "filePath", "validateMaterialFileType", "push", "displayName", "length", "updateMaterialChange", "removeFile", "fileIndex", "splice", "type", "data", "submitAgreement", "submitData", "buildSubmitData", "param", "fileOptType", "materialPlanFileList", "uploadMaterialList", "setTimeout", "navigateBack", "handleSave", "validateRequiredMaterials", "createModal", "title", "content", "confirmText", "cancelText", "confirm", "optRes", "fileRes", "all", "getMaterialByTypeAndStatus", "selectStMaterialStAndType", "materialOpts", "uploadedFiles", "uploadedFileMap", "dataJson", "JSON", "parse", "e", "map", "opt", "uploadedData", "firstData", "db<PERSON><PERSON><PERSON>", "f", "parsedData", "concat", "initPage", "options", "parseInt", "text", "label", "getMaterialByStId", "onLoad", "event", "fileToDelete", "realIndex", "findIndex", "fileFullPath", "wx", "createPage", "MiniProgramPage"], "mappings": "ooBA6IA,MAAMA,EAAY,IAAW,+CACvBC,EAAkB,IAAW,oFAE7B,MAAAC,EAAUC,EAAAA,YAAOD,SAAW,IAE5BE,MAACA,GAASC,uBACVC,EAAQF,EAAME,OAEdC,kBAACA,GAAqBH,EAAMI,QAAQ,qBAGpCC,EAAUC,EAAGA,KAAC,GACdC,EAAcD,EAAGA,KAAC,GAGlBE,EAAc,CAClBC,YAAa,UACbC,eAAgB,SAChBC,cAAe,WAIXC,EAASN,EAAAA,IAAI,CACjBO,KAAM,KACNC,WAAY,GACZC,cAAe,GACfC,eAAgB,OAIZC,EAA0BX,EAAGA,KAAC,GAG9BY,EAAWZ,EAAAA,IAAI,CACnBS,cAAe,KAIXI,EAAuBb,EAAGA,IAAC,IAG3Bc,EAAkBd,EAAGA,IAAC,IAItBe,EAAkBf,EAAGA,IAAC,IAItBgB,EAAkBC,EAAQA,UAAC,KAMxB,IAKHC,EAAoBC,IACxB,IAAKA,IAAaC,MAAMC,QAAQF,GAAW,MAAO,GAE3C,OADYA,EAASG,QAAeC,GAAAA,IAASA,EAAKC,gBAAkBD,EAAKE,UAAYF,EAAKG,SAgD7FC,EAA6BC,UAC7B,GAAChB,EAASiB,MAAMpB,cAKhB,IACCqB,EAAAC,MAACC,YAAY,UAGhB,MAAMC,EAAiB,CACrB1B,KAAMD,EAAOuB,MAAMtB,KACnBC,WAAYF,EAAOuB,MAAMrB,WACzBC,cAAeG,EAASiB,MAAMpB,cAC9ByB,gBAAiB,WAGbC,EAAAA,uBAAuBF,GAGtB3B,EAAAuB,MAAMpB,cAAgBG,EAASiB,MAAMpB,cAG5CE,EAAwBkB,OAAQ,QAC1BO,IAENC,EAAAA,MAAIC,cACJ1C,EAAM2C,KAAK,aACZ,OAAQC,GACPH,EAAAA,MAAcI,MAAA,QAAA,gDAAA,cAAeD,GAC7BH,EAAAA,MAAIC,aACL,MA7BC1C,EAAM2C,KAAK,cA0DTG,EAAwBC,IAC5B,MAAMC,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,IAGA7B,EAAgBc,MAAMc,KACT5B,EAAAc,MAAMc,GAAiB,CACrCI,GAAIH,EAAYI,YAAc,KAC9BL,gBACAM,aAAcL,EAAYK,aAC1BC,YAAaN,EAAYM,YACzBC,SAAUP,EAAYQ,SACtBjC,SAAU,GACVkC,aAAc,CAAC,CAACC,IAAK,GAAIzB,MAAO,KAChC0B,YAAY,IAIAxC,EAAAc,MAAMc,GAAeU,aAAe,IAAKT,EAAYS,cAAgB,IACrEtC,EAAAc,MAAMc,GAAeY,YAAa,IAI9CC,EAAc5B,MAAOe,IACrB,IAEE3B,EAAgBa,MAMd,MAAA4B,QAtID,IAAIC,SAASC,IAClBtB,EAAAA,MAAIuB,gBAAgB,CAClBC,SAAU,CAAC,UAAW,aACtBC,QAAUC,IACa,IAAjBA,EAAIC,SACNL,EAAQ,SACkB,IAAjBI,EAAIC,UACbL,EAAQ,SAGZM,KAAM,KACJN,EAAQ,YA4HZ,IAAKF,EACH,OAII,MAAAS,QAAeC,sBAAoB,CACvCC,MAAO,GACPC,QAAS,GACTC,aAAc,CAAC,QAAS,YACxBC,YAAa,6TAEbC,eAAgBf,EAEhBgB,WAAY,CACVC,YAAa,kBACb/B,iBAEFgC,WAAaC,MAEbC,UAAYC,QAIV,GAAAZ,EAAOa,UAAYb,EAAOY,cAAe,CAE3C,MAAMlC,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IACnE,GAAIC,EAAa,CACVA,EAAYzB,WACfyB,EAAYzB,SAAW,IAIzB,MAAM6D,EAAa,GACbC,EAAe,GAEdf,EAAAY,cAAcI,SAAgB3D,IAE7B,MAAAC,EAAiBD,EAAKC,gBAAkBD,EAAKG,KAC7CyD,EAAW5D,EAAK4D,SAGlBC,EAAwBA,yBAAC5D,EAAgB2D,GAE3CH,EAAWK,KAAK,IACX9D,EAEH+D,YAAa9D,GAAkBD,EAAKE,UAAYF,EAAKG,MAAQ,SAGlDuD,EAAAI,KAAK7D,GAAkB2D,GAAY,WAIhDH,EAAWO,OAAS,IACV3C,EAAAzB,SAASkE,QAAQL,GAERQ,EAAA7C,EAAe,WAAYC,EAAYzB,UAC5DkB,oEAAY,WAAYO,EAAYzB,WAIlC8D,EAAaM,OAAS,EACxB3F,EAAM2C,KAAK,GAAG0C,EAAaM,kBAE3B3F,EAAM2C,KAAK,SAEd,CACF,CACF,OAAQC,GACPH,EAAAA,MAAcI,MAAA,QAAA,gDAAA,aAAcD,EAC7B,GAIGiD,EAAa,CAAC9C,EAAe+C,KACjC,MAAM9C,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,GAAgBA,EAAYzB,WAErByB,EAAAzB,SAASwE,OAAOD,EAAW,GAClBF,EAAA7C,EAAe,WAAYC,EAAYzB,UAE5DvB,EAAM2C,KAAK,WAIPiD,EAAuB,CAAC7C,EAAeiD,EAAMC,KACjD,MAAMjD,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,IAEA7B,EAAgBc,MAAMc,KACT5B,EAAAc,MAAMc,GAAiB,CACrCI,GAAIH,EAAYI,YAAc,KAC9BL,gBACAM,aAAcL,EAAYK,aAC1BC,YAAaN,EAAYM,YACzBC,SAAUP,EAAYQ,SACtBjC,SAAU,GACVkC,aAAc,CAAC,CAACC,IAAK,GAAIzB,MAAO,KAChC0B,YAAY,IAIH,aAATqC,EACc7E,EAAAc,MAAMc,GAAexB,SAAW,IAAK0E,GAAQ,IAC3C,iBAATD,IACO7E,EAAAc,MAAMc,GAAeU,aAAe,IAAKwC,GAAQ,KAGnD9E,EAAAc,MAAMc,GAAeY,YAAa,IAS9CuC,EAAkBlE,UAClB,IACCE,EAAAC,MAACC,YAAY,UAChB/B,EAAY4B,OAAQ,EAGpB,MAAMkE,EAAaC,EAAAA,gBAAgBjF,EAAgBc,OAE/C,GAAsB,IAAtBkE,EAAWR,OAEb,YADA3F,EAAM2C,KAAK,aAKb,MAAM0D,EAAQ,CACZ1F,KAAMD,EAAOuB,MAAMtB,KACnBC,WAAYF,EAAOuB,MAAMrB,WACzBE,eAAgBJ,EAAOuB,MAAMnB,eAC7BwF,YAAa,IACbC,qBAAsBJ,SAIlBK,EAAAA,mBAAmBH,GAEzBrG,EAAM2C,KAAK,QAGX8D,YAAW,KACThE,EAAAA,MAAIiE,iBACH,IAEJ,OAAQ9D,GACPH,EAAAA,oEAAc,QAASG,EAC3B,CAAY,QACRH,EAAGN,MAACO,cACJrC,EAAY4B,OAAQ,CACrB,GAIG0E,EAAa3E,UAEb,GA/CG4E,EAAAA,0BAA0B1F,EAAgBe,MAAOd,EAAgBc,MAAOjC,GAoD3E,WACmB6G,cAAY,CAC/BC,MAAO,OACPC,QAAS,qBACTC,YAAa,KACbC,WAAY,QAGHC,eACHhB,GAET,OAAQtD,GACPH,EAAAA,MAAAI,MAAA,QAAA,gDAAc,WAAYD,SAEpBsD,GACP,GAIG1D,EAAWR,UACX,GAACtB,EAAOuB,MAAMtB,KAAd,CAEJR,EAAQ8B,OAAQ,EACZ,IAEF,MAAOkF,EAAQC,SAAiBtD,QAAQuD,IAAI,CAC1CC,6BAA2B,CACzB1G,WAAYF,EAAOuB,MAAMrB,WACzBC,cAAeH,EAAOuB,MAAMpB,gBAE9B0G,4BAA0B,CACxB5G,KAAMD,EAAOuB,MAAMtB,KACnB2F,YAAa,IACbxF,eAAgBJ,EAAOuB,MAAMnB,mBAK3B0G,EAAeL,EAAOlB,MAAQ,GAG9BwB,EAAgBL,EAAQnB,MAAQ,GAGhCyB,EAAkB,CAAE,EAC1BD,EAAcnC,SAAgB3D,IACvB+F,EAAgB/F,EAAKoB,iBACR2E,EAAA/F,EAAKoB,eAAiB,IAIxC,IAAIxB,EAAW,GACf,GAAII,EAAKgG,SACH,IAEWpG,EADgB,iBAAlBI,EAAKgG,SACHC,KAAKC,MAAMlG,EAAKgG,UAEhBhG,EAAKgG,QAEnB,OAAQG,GACPrF,EAAAA,MAAaI,MAAA,OAAA,gDAAA,cAAeiF,GAC5BvG,EAAW,EACZ,CAICC,MAAMC,QAAQF,IAAaA,EAASoE,OAAS,GAC/B+B,EAAA/F,EAAKoB,eAAe0C,KAAK,CACvCrC,WAAYzB,EAAKwB,GACjB5B,gBAMUL,EAAAe,MAAQuF,EAAaO,KAAWC,IACxC,MAAAC,EAAeP,EAAgBM,EAAI7E,IACzC,IAAI5B,EAAW,GACXkC,EAAe,CAAC,CAACC,IAAK,GAAIzB,MAAO,KACjCmB,EAAa,KACbI,EAAWwE,EAAIxE,UAAY,IAE3B,GAAAyE,GAAgBA,EAAatC,OAAS,EAAG,CACrC,MAAAuC,EAAYD,EAAa,GAC/B7E,EAAa8E,EAAU9E,WAEvB,MAAM+E,EAAWV,EAAcxE,SAAUmF,EAAEjF,KAAO+E,EAAU9E,aAC5D,GAAI+E,EACF,GAAiB,MAAb3E,EAEE,IACF,GAAI2E,EAASR,SAAU,CACf,MAAAU,EAA0C,iBAAtBF,EAASR,SAC7BC,KAAKC,MAAMM,EAASR,UACpBQ,EAASR,SAGElE,EADbjC,MAAMC,QAAQ4G,GACDA,EAAW1C,OAAS,EAAI0C,EAAa,CAAC,CAAC3E,IAAK,GAAIzB,MAAO,KAC7DoG,GAAoC,iBAAfA,EACf,CAACA,GAED,CAAC,CAAC3E,IAAK,GAAIzB,MAAO,IAEpC,CACF,OAAQ6F,GACPrF,EAAAA,MAAAI,MAAA,OAAA,gDAAa,YAAaiF,GAC1BrE,EAAe,CAAC,CAACC,IAAK,GAAIzB,MAAO,IAClC,MAGDgG,EAAa3C,SAAgBW,IAC3B1E,EAAWA,EAAS+G,OAAOrC,EAAK1E,UAAY,MAInD,CAEM,MAAA,IACFyG,EACHzG,WACAkC,eACAD,WACAJ,iBAKYlC,EAAAe,MAAMqD,SAAgBpC,IACpB/B,EAAAc,MAAMiB,EAAKC,IAAM,CAC/BA,GAAID,EAAKE,YAAc,KACvBL,cAAeG,EAAKC,GACpBE,aAAcH,EAAKG,aACnBC,YAAaJ,EAAKI,YAClBC,SAAUL,EAAKM,UAAY,IAC3BjC,SAAU2B,EAAK3B,UAAY,GAC3BkC,aAAcP,EAAKO,cAAgB,CAAC,CAACC,IAAK,GAAIzB,MAAO,KACrD0B,YAAY,KAGjB,OAAQf,GACPH,EAAAA,MAAAI,MAAA,QAAA,gDAAc,UAAWD,GACzB5C,EAAM2C,KAAK,SACf,CAAY,QACRxC,EAAQ8B,OAAQ,CACjB,CA5HuB,GA+IpBsG,EAAWvG,MAAOwG,EAAU,MAU5B,GARJ9H,EAAOuB,MAAQ,CACbtB,KAAM8H,SAASD,EAAQ7H,OAAS,MAXlCM,EAAqBgB,MAAQhC,EAAkBgC,MAAM8F,KAAa7E,IAAA,CAChEwF,KAAMxF,EAAKyF,MACX1G,MAAOiB,EAAKjB,UAEdQ,EAAYN,MAAAU,MAAA,MAAA,gDAAA,YAAa5B,EAAqBgB,OAczCvB,EAAOuB,MAAMpB,cAoBN2B,SAlBN,IACF,MAAM2B,QAAYyE,EAAAA,kBAAkBlI,EAAOuB,MAAMtB,MAC7CwD,EAAI8B,MAAQ9B,EAAI8B,KAAKpF,eAChBH,EAAAuB,MAAMpB,cAAgBsD,EAAI8B,KAAKpF,cAC/BH,EAAAuB,MAAMrB,WAAauD,EAAI8B,KAAKrF,WAEzB4B,KAGVzB,EAAwBkB,OAAQ,CAEnC,OAAQW,GACPH,EAAAA,MAAcI,MAAA,QAAA,gDAAA,cAAeD,GAE7B7B,EAAwBkB,OAAQ,CACjC,UAQCC,EAAA2G,QAAEL,IACND,EAASC,4oBAjec,EAACzF,EAAe+C,EAAWgD,KAElD,MAAM9F,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC/D,IAACC,IAAgBA,EAAYzB,SAAU,OAErC,MACAwH,EADazH,EAAiB0B,EAAYzB,UAChBuE,GAG1BkD,EAAYhG,EAAYzB,SAAS0H,WAAUtH,GAC7CA,IAASoH,GACRpH,EAAKC,iBAAmBmH,EAAanH,gBAAkBD,EAAKuH,eAAiBH,EAAaG,cAC1FvH,EAAKE,WAAakH,EAAalH,UAAYF,EAAK/B,UAAYmJ,EAAanJ,SACzE+B,EAAKC,iBAAmBkH,EAAMjH,UAAYF,EAAKuH,eAAiBJ,EAAMvD,YAGrD,IAAlByD,EACFnD,EAAW9C,EAAeiG,GAE1BvG,EAAAA,MAAaI,MAAA,OAAA,gDAAA,6eAwCE,CAACE,IAClB,MAAMC,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,IAEAA,EAAYS,eACfT,EAAYS,aAAe,IAG7BT,EAAYS,aAAagC,KAAK,CAAC/B,IAAK,GAAIzB,MAAO,KAC/Ca,EAAqBC,sLAID,EAACA,EAAeZ,KACpC,MAAMa,EAAc9B,EAAgBe,MAAMgB,MAAaC,GAAAA,EAAKC,KAAOJ,IAC9DC,GAAgBA,EAAYS,eAG7BT,EAAYS,aAAakC,QAAU,IAE3B3C,EAAAS,aAAasC,OAAO5D,EAAO,GACvCW,EAAqBC,qTClTvBoG,GAAGC,WAAWC"}