<template>
  <view class="container">
    <!-- 导航栏 -->
    <custom-nav title="材料上传" :showLeft="true"></custom-nav>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-wrapper">
      <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
    </view>

    <!-- 页面内容 -->
    <view v-else class="material-upload">
      <!-- 学生在读类型选择 -->
      <view v-if="showStudentStatusSelect" class="select-section">
        <view class="section-title">请选择学生在读类型</view>
        <text class="select-tip">请先选择该学生的在读类型，以便为您展示对应的材料配置：</text>
        <view class="picker-wrapper">
          <uni-data-picker placeholder="请选择学生在读类型" popup-title="请选择学生在读类型"
                           :localdata="studentStatusOptions" v-model="formData.studentStatus"></uni-data-picker>
        </view>
        <view class="select-actions">
          <button class="action-btn confirm-btn" @tap="handleStudentStatusConfirm">
            确认
          </button>
        </view>
      </view>

      <!-- 材料列表为空 -->
      <view v-else-if="!materialOptList.length" class="empty-section">
        <view class="empty-icon">📋</view>
        <text class="empty-text">暂无可上传的材料配置</text>
      </view>

      <!-- 材料列表 -->
      <view v-else class="materials-container">
        <view v-for="item in materialOptList" :key="item.id" class="material-item"
              :class="{ 'required': item.isRequired === 'Y' }">

          <!-- 材料头部信息 -->
          <view class="material-header">
            <view class="header-left">
              <text class="material-title">{{ item.materialType }}</text>
              <view class="material-tags">
                <text class="type-tag" :class="item.dataType === '1' ? 'file-tag' : 'text-tag'">
                  {{ item.dataType === '1' ? '文件' : '文本' }}
                </text>
                <text v-if="item.isRequired === 'Y'" class="required-tag">必填</text>
              </view>
            </view>
          </view>

          <text class="material-desc">{{ item.description }}</text>

          <!-- 材料内容 -->
          <view class="material-content">
            <!-- 文件类型 -->
            <view v-if="item.dataType === '1'" class="file-container">
              <!-- 已上传文件列表 - 只有当有有效文件时才显示 -->
              <view v-if="getValidFileList(item.fileList).length > 0" class="file-list">
                <DocumentPreview
                    v-for="(file, fileIndex) in getValidFileList(item.fileList)"
                    :key="fileIndex"
                    :file-path="(fileUrl + file.filePath) || file.fileFullPath || ''"
                    :file-name="file.sourceFileName || file.fileName || file.name || '未知文件'"
                    :allow-view="!!((fileUrl + file.filePath) || file.fileFullPath)"
                    :allow-download="!!((fileUrl + file.filePath) || file.fileFullPath)"
                    :allow-delete="true"
                    @delete="handleFileDelete(item.id, fileIndex, $event)"
                    class="material-file-item"
                />
              </view>

              <!-- 上传按钮 -->
              <view class="upload-btn" @tap="chooseFiles(item.id)">
                <text class="upload-icon">+</text>
                <view class="upload-content">
                  <text class="upload-text">{{
                      item.fileList && item.fileList.length > 0 ? '继续添加' : '上传文件'
                    }}
                  </text>
                  <text class="upload-tip">
                    <!-- #ifdef MP-WEIXIN -->
                    支持文档、图片格式（可选择相册或聊天记录）
                    <!-- #endif -->
                    <!-- #ifndef MP-WEIXIN -->
                    {{ isWechatBrowser ? '支持文档、图片格式（微信浏览器）' : '支持文档、图片格式' }}
                    <!-- #endif -->
                  </text>
                  <!-- 调试信息 -->
                  <text v-if="isWechatBrowser" class="debug-info">
                    微信浏览器环境 - 可能无法正确上传
                  </text>
                </view>
              </view>
            </view>

            <!-- 文本类型 -->
            <view v-else-if="item.dataType === '2'" class="text-container">
              <view class="text-header">
                <text class="text-title">账号信息</text>
                <text class="add-btn" @tap="addTextRow(item.id)">
                  添加
                </text>
              </view>

              <view class="text-rows">
                <view v-for="(row, index) in item.textDataList" :key="index" class="text-row">
                  <input v-model="row.key" @input="handleTextDataChange(item.id)" placeholder="例：账号"
                         class="text-input key-input"/>
                  <input v-model="row.value" @input="handleTextDataChange(item.id)" placeholder="例：12345678"
                         class="text-input value-input"/>
                  <text class="delete-row-btn" @tap="removeTextRow(item.id, index)"
                        :class="{ disabled: item.textDataList.length <= 1 }">
                    删除
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部空间，防止内容被固定按钮遮挡 -->
      <view class="bottom-spacing"></view>

      <!-- 操作按钮 -->
      <view v-if="materialOptList.length > 0" class="action-section">
        <button class="action-btn save-btn" @tap="handleSave" :disabled="saveLoading">
          <text v-if="saveLoading">保存中...</text>
          <text v-else>保存材料</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref, computed, getCurrentInstance} from 'vue'
import config from '@/config/environment'
import {onLoad} from '@dcloudio/uni-app'
import {getMaterialByTypeAndStatus} from '@/api/home/<USER>'
import {getMaterialByStId, saveOrUpdateStMaterial} from '@/api/home/<USER>'
import {selectStMaterialStAndType, uploadMaterialList} from '@/api/home/<USER>'
import {chooseAndUploadFile, getFileExtension, validateMaterialFileType} from '@/common/fileUtils.js'
import {createModal} from '@/common/modal'
import {validateRequiredMaterials, buildSubmitData} from './materialValidation.js'
import CustomNav from '@/components/custom-nav/custom-nav.vue'
import DocumentPreview from '@/components/document-preview/index.vue'

const fileUrl = config.fileUrl || '';
// 获取 toast 实例
const {proxy} = getCurrentInstance()
const toast = proxy.toast
// 字典数据
const {t_st_study_status} = proxy.useDict('t_st_study_status')

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)

// 加载中文本
const loadingText = {
  contentdown: '正在加载...',
  contentrefresh: '加载中...',
  contentnomore: '没有更多数据了'
}

// 学生信息
const stInfo = ref({
  stId: null,
  stDecaType: '',
  studentStatus: '',
  materialPlanId: null
})

// 学生在读类型选择相关
const showStudentStatusSelect = ref(false)

// 表单数据
const formData = ref({
  studentStatus: ''
})

// 学生在读类型选项数据
const studentStatusOptions = ref([])

// 材料配置列表
const materialOptList = ref([])


// 材料文件变更记录
const materialChanges = ref({})


// 检测是否在微信浏览器中
const isWechatBrowser = computed(() => {
  // #ifdef H5
  if (typeof navigator !== 'undefined') {
    return /micromessenger/i.test(navigator.userAgent)
  }
  // #endif
  return false
})


// 获取有效的文件列表
const getValidFileList = (fileList) => {
  if (!fileList || !Array.isArray(fileList)) return []
  const validFiles = fileList.filter(file => file && (file.sourceFileName || file.fileName || file.name))
  return validFiles
}

// 显示文件来源选择器（微信小程序专用）
const showFileSourceSelector = () => {
  return new Promise((resolve) => {
    uni.showActionSheet({
      itemList: ['从手机相册选择（仅图片）', '从微信聊天记录选择（所有文件）'],
      success: (res) => {
        if (res.tapIndex === 0) {
          resolve('album') // 从相册选择
        } else if (res.tapIndex === 1) {
          resolve('chat') // 从聊天记录选择
        }
      },
      fail: () => {
        resolve(null) // 用户取消
      }
    })
  })
}

// 处理文件删除
const handleFileDelete = (materialOptId, fileIndex, event) => {
  // 由于我们使用了过滤后的文件列表，需要找到原始文件列表中的真实索引
  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)
  if (!materialOpt || !materialOpt.fileList) return

  const validFiles = getValidFileList(materialOpt.fileList)
  const fileToDelete = validFiles[fileIndex]

  // 在原始文件列表中找到这个文件的真实索引
  const realIndex = materialOpt.fileList.findIndex(file =>
      file === fileToDelete ||
      (file.sourceFileName === fileToDelete.sourceFileName && file.fileFullPath === fileToDelete.fileFullPath) ||
      (file.fileName === fileToDelete.fileName && file.fileUrl === fileToDelete.fileUrl) ||
      (file.sourceFileName === event.fileName && file.fileFullPath === event.filePath)
  )

  if (realIndex !== -1) {
    removeFile(materialOptId, realIndex)
  } else {
    console.warn('未找到要删除的文件')
  }
}

// 处理学生在读类型确认
const handleStudentStatusConfirm = async () => {
  if (!formData.value.studentStatus) {
    toast.show('请选择学生在读类型')
    return
  }

  try {
    uni.showLoading('提交中...')

    // 保存学生资料信息
    const stMaterialData = {
      stId: stInfo.value.stId,
      stDecaType: stInfo.value.stDecaType,
      studentStatus: formData.value.studentStatus,
      stFileCreStatus: '1' // 初始状态待上传
    }

    await saveOrUpdateStMaterial(stMaterialData)

    // 更新学生信息
    stInfo.value.studentStatus = formData.value.studentStatus

    // 隐藏选择界面，加载材料配置
    showStudentStatusSelect.value = false
    await loadData()

    uni.hideLoading()
    toast.show('学生在读类型设置成功')
  } catch (error) {
    console.error('保存学生在读类型失败:', error)
    uni.hideLoading()
  }
}

// 添加文本行
const addTextRow = (materialOptId) => {
  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)
  if (!materialOpt) return

  if (!materialOpt.textDataList) {
    materialOpt.textDataList = []
  }

  materialOpt.textDataList.push({key: '', value: ''})
  handleTextDataChange(materialOptId)
}

// 删除文本行
const removeTextRow = (materialOptId, index) => {
  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)
  if (!materialOpt || !materialOpt.textDataList) return

  // 至少保留一行
  if (materialOpt.textDataList.length <= 1) return

  materialOpt.textDataList.splice(index, 1)
  handleTextDataChange(materialOptId)
}

// 文本数据变更处理
const handleTextDataChange = (materialOptId) => {
  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)
  if (!materialOpt) return

  // 更新变更记录
  if (!materialChanges.value[materialOptId]) {
    materialChanges.value[materialOptId] = {
      id: materialOpt.planFileId || null,
      materialOptId: materialOptId,
      materialType: materialOpt.materialType,
      description: materialOpt.description,
      planType: materialOpt.dataType,
      fileList: [],
      textDataList: [{key: '', value: ''}],
      isModified: false
    }
  }

  materialChanges.value[materialOptId].textDataList = [...(materialOpt.textDataList || [])]
  materialChanges.value[materialOptId].isModified = true
}

// 选择文件
const chooseFiles = async (materialOptId) => {
  try {
    // 在微信浏览器中显示特殊提示和调试信息
    if (isWechatBrowser.value) {
    } else {
    }

    // #ifdef MP-WEIXIN
    // 微信小程序环境，先让用户选择文件来源
    const sourceType = await showFileSourceSelector()
    if (!sourceType) {
      return // 用户取消选择
    }
    // #endif

    const result = await chooseAndUploadFile({
      count: 10, // 最多选择10个文件
      maxSize: 10, // 10MB限制
      allowedTypes: ['image', 'document'], // 支持图片、文档、视频和其他文件类型
      acceptTypes: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,.csv,image/*', // H5环境下精确指定文件类型
      // #ifdef MP-WEIXIN
      fileSourceType: sourceType, // 传递文件来源类型
      // #endif
      uploadData: {
        fileBizType: 'material_upload',
        materialOptId: materialOptId
      },
      onProgress: (progress) => {
      },
      onSuccess: (uploadResults) => {
      }
    })

    if (result.uploaded && result.uploadResults) {
      // 上传成功，更新文件列表
      const materialOpt = materialOptList.value.find(item => item.id === materialOptId)
      if (materialOpt) {
        if (!materialOpt.fileList) {
          materialOpt.fileList = []
        }

        // 验证并添加新上传的文件到列表
        const validFiles = []
        const invalidFiles = []

        result.uploadResults.forEach(file => {
          // 使用原始文件名进行验证
          const sourceFileName = file.sourceFileName || file.name
          const filePath = file.filePath

          // 使用增强的文件类型验证，支持从 filePath 获取扩展名
          if (validateMaterialFileType(sourceFileName, filePath)) {
            // 保持原始数据结构，不修改任何字段名
            validFiles.push({
              ...file, // 保持所有原始字段
              // 确保有必要的字段用于显示
              displayName: sourceFileName || file.fileName || file.name || '未知文件'
            })
          } else {
            invalidFiles.push(sourceFileName || filePath || '未知文件')
          }
        })

        if (validFiles.length > 0) {
          materialOpt.fileList.push(...validFiles)
          // 更新变更记录
          updateMaterialChange(materialOptId, 'fileList', materialOpt.fileList)
          console.log('文件列表已更新:', materialOpt.fileList)
        }

        // 显示结果提示
        if (invalidFiles.length > 0) {
          toast.show(`${invalidFiles.length}个文件格式不支持`)
        } else {
          toast.show('文件上传成功')
        }
      }
    }
  } catch (error) {
    console.error('文件选择或上传失败:', error)
  }
}

// 删除文件
const removeFile = (materialOptId, fileIndex) => {
  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)
  if (!materialOpt || !materialOpt.fileList) return

  materialOpt.fileList.splice(fileIndex, 1)
  updateMaterialChange(materialOptId, 'fileList', materialOpt.fileList)

  toast.show('文件已删除')
}

// 更新材料变更记录
const updateMaterialChange = (materialOptId, type, data) => {
  const materialOpt = materialOptList.value.find(item => item.id === materialOptId)
  if (!materialOpt) return

  if (!materialChanges.value[materialOptId]) {
    materialChanges.value[materialOptId] = {
      id: materialOpt.planFileId || null,
      materialOptId: materialOptId,
      materialType: materialOpt.materialType,
      description: materialOpt.description,
      planType: materialOpt.dataType,
      fileList: [],
      textDataList: [{key: '', value: ''}],
      isModified: false
    }
  }

  if (type === 'fileList') {
    materialChanges.value[materialOptId].fileList = [...(data || [])]
  } else if (type === 'textDataList') {
    materialChanges.value[materialOptId].textDataList = [...(data || [])]
  }

  materialChanges.value[materialOptId].isModified = true
}

// 验证必填材料（使用工具类）
const validateMaterials = () => {
  return validateRequiredMaterials(materialOptList.value, materialChanges.value, toast)
}

// 提交材料数据
const submitAgreement = async () => {
  try {
    uni.showLoading('保存中...')
    saveLoading.value = true

    // 构建提交数据 - 使用工具类
    const submitData = buildSubmitData(materialChanges.value)

    if (submitData.length === 0) {
      toast.show('没有检测到文件变更')
      return
    }

    // 构建请求参数
    const param = {
      stId: stInfo.value.stId,
      stDecaType: stInfo.value.stDecaType,
      materialPlanId: stInfo.value.materialPlanId,
      fileOptType: '1',
      materialPlanFileList: submitData
    }

    // 调用批量提交接口
    await uploadMaterialList(param)

    toast.show('保存成功')

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 500)

  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    uni.hideLoading();
    saveLoading.value = false
  }
}

// 保存材料
const handleSave = async () => {
  // 验证必填材料
  if (!validateMaterials()) {
    return
  }

  // 显示确认对话框
  try {
    const result = await createModal({
      title: '确认提交',
      content: '确定要提交该协议吗？提交后将不可修改',
      confirmText: '确定',
      cancelText: '取消'
    })

    if (result.confirm) {
      await submitAgreement()
    }
  } catch (error) {
    console.error('确认对话框错误:', error)
    // 如果对话框出错，直接提交
    await submitAgreement()
  }
}

// 加载材料配置和已上传文件
const loadData = async () => {
  if (!stInfo.value.stId) return

  loading.value = true
  try {
    // 根据学生类型和在读状态查询材料配置
    const [optRes, fileRes] = await Promise.all([
      getMaterialByTypeAndStatus({
        stDecaType: stInfo.value.stDecaType,
        studentStatus: stInfo.value.studentStatus
      }),
      selectStMaterialStAndType({
        stId: stInfo.value.stId,
        fileOptType: '1',
        materialPlanId: stInfo.value.materialPlanId
      })
    ])

    // 获取材料配置列表
    const materialOpts = optRes.data || []

    // 获取已上传文件列表
    const uploadedFiles = fileRes.data || []

    // 创建已上传文件的映射
    const uploadedFileMap = {}
    uploadedFiles.forEach(file => {
      if (!uploadedFileMap[file.materialOptId]) {
        uploadedFileMap[file.materialOptId] = []
      }

      // 解析 dataJson 字段
      let fileList = []
      if (file.dataJson) {
        try {
          if (typeof file.dataJson === 'string') {
            fileList = JSON.parse(file.dataJson)
          } else {
            fileList = file.dataJson
          }
        } catch (e) {
          console.warn('解析文件JSON失败:', e)
          fileList = []
        }
      }

      // 将文件信息添加到映射中
      if (Array.isArray(fileList) && fileList.length > 0) {
        uploadedFileMap[file.materialOptId].push({
          planFileId: file.id,
          fileList: fileList
        })
      }
    })

    // 合并配置数据和已上传文件数据
    materialOptList.value = materialOpts.map(opt => {
      const uploadedData = uploadedFileMap[opt.id]
      let fileList = []
      let textDataList = [{key: '', value: ''}]
      let planFileId = null
      let dataType = opt.dataType || '1'

      if (uploadedData && uploadedData.length > 0) {
        const firstData = uploadedData[0]
        planFileId = firstData.planFileId

        const dbRecord = uploadedFiles.find(f => f.id === firstData.planFileId)
        if (dbRecord) {
          if (dataType === '2') {
            // 文本类型，解析文本数据
            try {
              if (dbRecord.dataJson) {
                const parsedData = typeof dbRecord.dataJson === 'string'
                    ? JSON.parse(dbRecord.dataJson)
                    : dbRecord.dataJson

                if (Array.isArray(parsedData)) {
                  textDataList = parsedData.length > 0 ? parsedData : [{key: '', value: ''}]
                } else if (parsedData && typeof parsedData === 'object') {
                  textDataList = [parsedData]
                } else {
                  textDataList = [{key: '', value: ''}]
                }
              }
            } catch (e) {
              console.warn('解析文本数据失败:', e)
              textDataList = [{key: '', value: ''}]
            }
          } else {
            // 文件类型，合并所有已上传的文件
            uploadedData.forEach(data => {
              fileList = fileList.concat(data.fileList || [])
            })
          }
        }
      }

      return {
        ...opt,
        fileList: fileList,
        textDataList: textDataList,
        dataType: dataType,
        planFileId: planFileId
      }
    })

    // 初始化变更记录
    materialOptList.value.forEach(item => {
      materialChanges.value[item.id] = {
        id: item.planFileId || null,
        materialOptId: item.id,
        materialType: item.materialType,
        description: item.description,
        planType: item.dataType || '1',
        fileList: item.fileList || [],
        textDataList: item.textDataList || [{key: '', value: ''}],
        isModified: false
      }
    })
  } catch (error) {
    console.error('加载数据失败:', error)
    toast.show('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 初始化学生在读类型选项数据
const initStudentStatusOptions = () => {
  // 将字典数据转换为uni-data-picker需要的格式
  studentStatusOptions.value = t_st_study_status.value.map(item => ({
    text: item.label,
    value: item.value
  }))
  console.log('学生在读类型选项:', studentStatusOptions.value)
}

// 页面初始化
const initPage = async (options = {}) => {
  // 从页面参数获取学生信息
  stInfo.value = {
    stId: parseInt(options.stId) || null,
  }

  // 初始化学生在读类型选项数据
  initStudentStatusOptions()

  // 检查学生在读类型
  if (!stInfo.value.studentStatus) {
    // 先查询学生资料信息
    try {
      const res = await getMaterialByStId(stInfo.value.stId)
      if (res.data && res.data.studentStatus) {
        stInfo.value.studentStatus = res.data.studentStatus
        stInfo.value.stDecaType = res.data.stDecaType
        // 有在读类型，直接加载数据
        loadData()
      } else {
        // 没有在读类型，显示选择界面
        showStudentStatusSelect.value = true
      }
    } catch (error) {
      console.error('获取学生资料信息失败:', error)
      // 如果查询失败，也显示选择界面
      showStudentStatusSelect.value = true
    }
  } else {
    // 已有在读类型，直接加载数据
    loadData()
  }
}

// 使用 onLoad 获取页面参数
onLoad((options) => {
  initPage(options)
})
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;
}

.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300rpx;
}

.material-upload {
  padding: 30rpx;
}

// 选择学生在读类型区域
.select-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.select-tip {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
  display: block;
}

// uni-data-picker 样式
.picker-wrapper {
  margin-bottom: 30rpx;
}

:deep(.uni-data-tree) {
  padding: 10rpx 0;
}

:deep(.uni-data-pickerview__item) {
  line-height: 80rpx;
  padding: 0 20rpx;
}

:deep(.uni-data-picker__input-text) {
  font-size: 28rpx;
}

:deep(.input-value) {
  padding: 24rpx 32rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
  transition: border-color 0.3s ease;
  min-height: 60rpx;

  &:active {
    border-color: var(--nuodun-primary-color);
  }
}

.select-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  padding: 0 20rpx;
}

.action-btn {
  width: 45%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  border-radius: 44rpx;
  margin: 0 10rpx;
  border: none;
  text-align: center;
}

.confirm-btn {
  background: linear-gradient(135deg, var(--nuodun-primary-color, #1890ff) 0%, var(--nuodun-primary-color-light, #4dabff) 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(var(--nuodun-primary-color-rgb, 24, 144, 255), 0.3);
}

.reject-btn {
  background-color: #fff;
  color: #ff3b30;
  border: 1px solid #ff3b30;
}

// 空状态
.empty-section {
  background-color: #fff;
  padding: 60rpx 30rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

// 材料容器 - 整体卡片
.materials-container {
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
}

// 材料项
.material-item {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &.required {
    border-left: 4rpx solid #ff4757;
    padding-left: 26rpx;
  }
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.header-left {
  flex: 1;
}

.material-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.material-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.type-tag,
.required-tag {
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
  font-weight: 500;
}

.file-tag {
  background-color: #e3f2fd;
  color: #1976d2;
}

.text-tag {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.required-tag {
  background-color: #ffebee;
  color: #d32f2f;
}

.material-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.material-content {
  margin-top: 16rpx;
}

// 文件容器
.file-container {
  width: 100%;
}

// 文件列表
.file-list {
  margin-bottom: 12rpx;
}

// 材料文件项样式
.material-file-item {
  margin-bottom: 12rpx;

  :deep(.file-card) {
    margin-bottom: 0;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid #f0f0f0;
    background-color: #fff;
  }

  :deep(.file-card-btn) {
    font-size: 24rpx;
    padding: 6rpx 16rpx;
  }

  :deep(.file-card-icon) {
    font-size: 32rpx;
  }

  :deep(.file-card-name) {
    font-size: 26rpx;
  }
}

// 上传按钮
.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  border: 1rpx dashed #ddd;
  border-radius: 6rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;

  &:active {
    border-color: var(--nuodun-primary-color);
    background-color: rgba(24, 144, 255, 0.02);
  }
}

.upload-icon {
  font-size: 20rpx;
  color: var(--nuodun-primary-color);
  margin-right: 12rpx;
  font-weight: bold;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-text {
  font-size: 24rpx;
  color: var(--nuodun-primary-color);
  margin-bottom: 4rpx;
}

.upload-tip {
  font-size: 20rpx;
  color: #999;
  line-height: 1.2;
}

.debug-info {
  font-size: 18rpx;
  color: #ff6b35;
  line-height: 1.2;
  margin-top: 4rpx;
  text-align: center;
}

// 文本容器
.text-container {
  width: 100%;
}

.text-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.text-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
}

.add-btn {
  color: var(--nuodun-primary-color);
  font-size: 22rpx;
  padding: 8rpx 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    color: var(--nuodun-primary-dark);
    transform: scale(0.95);
  }
}

.text-rows {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.text-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.text-input {
  flex: 1;
  height: 60rpx;
  padding: 0 16rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #333;
  transition: all 0.3s ease;

  &.key-input {
    flex: 1;
  }

  &.value-input {
    flex: 2;
  }

  &:focus {
    border-color: var(--nuodun-primary-color);
    background-color: #fff;
  }

  &::placeholder {
    color: #999;
  }
}

.delete-row-btn {
  color: #ff4757;
  font-size: 22rpx;
  padding: 8rpx 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &.disabled {
    color: #ccc;
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:not(.disabled):active {
    color: #ff3742;
    transform: scale(0.95);
  }
}

// 底部操作按钮 - 与agreementSign.vue保持一致
.action-section {
  background-color: #fff;
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  text-align: center;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
}

.save-btn {
  background: linear-gradient(135deg, var(--nuodun-primary-color) 0%, var(--nuodun-primary-color-light, #4dabff) 100%);
  color: #fff !important;

  &[disabled] {
    opacity: 0.6;
    background: #ccc;
    color: #999 !important;
    box-shadow: none;
  }

  &:not([disabled]):active {
    transform: scale(0.98);
  }
}

.bottom-spacing {
  height: 130rpx;
}

// 响应式设计和动画效果
@media (max-width: 750rpx) {
  .page-content {
    padding: 20rpx;
  }

  .material-item {
    padding: 24rpx;
  }

  .material-title {
    font-size: 28rpx;
  }

  .text-row {
    flex-direction: column;
    gap: 12rpx;
  }

  .text-input {
    width: 100%;

    &.key-input,
    &.value-input {
      flex: none;
    }
  }

  .delete-row-btn {
    align-self: flex-end;
    width: 50rpx;
    height: 50rpx;
    font-size: 28rpx;
  }

  .bottom-actions {
    padding: 15rpx 20rpx;
  }

  .save-btn {
    height: 76rpx;
    font-size: 28rpx;
  }
}

// 动画效果
.material-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  :root {
    --nuodun-bg-color: #1c1c1e;
    --nuodun-card-bg: #2c2c2e;
    --nuodun-text-primary: #ffffff;
    --nuodun-text-secondary: #aeaeb2;
    --nuodun-text-placeholder: #8e8e93;
    --nuodun-border-color: #38383a;
    --nuodun-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  }
}
</style>