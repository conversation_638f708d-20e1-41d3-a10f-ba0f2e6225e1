<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9145f901-5d7e-46c6-9112-0ef457831d44" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../nuodun-file/server/nginx-1.18.0/logs/access.log" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-file/server/nginx-1.18.0/logs/access.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-file/server/nginx-1.18.0/logs/error.log" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-file/server/nginx-1.18.0/logs/error.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-file/服务器/nginx-1.18.0/logs/error.log" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-file/服务器/nginx-1.18.0/logs/error.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-file/需求/2025-07-21-优化后.sql" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-file/需求/2025-07-21-优化后.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/api/open.js" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/api/open.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/auth.js" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/auth.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/fileUtils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/fileUtils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/jsencrypt-wrapper.js" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/jsencrypt-wrapper.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/loading.js" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/loading.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/toast.js" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/common/toast.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/custom-nav/custom-nav.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/custom-nav/custom-nav.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/document-preview/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/document-preview/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/file-office/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/file-office/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/school-application-info/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/components/school-application-info/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/config/environment.js" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/config/environment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/docs/文件上传类型处理修复说明.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/home/<USER>/detail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/home/<USER>/detail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/home/<USER>/detail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/home/<USER>/detail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/home/<USER>/materialUpload.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/home/<USER>/materialUpload.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/login/bindPhone.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/login/bindPhone.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/login/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/login/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/mine/agreements/stInfo/contract/agreementDetail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/mine/agreements/stInfo/contract/agreementDetail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/mine/agreements/stInfo/school/agreementDetail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/mine/agreements/stInfo/school/agreementDetail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/tabbar/tabbar.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/tabbar/tabbar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/transition/filePreview.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/transition/filePreview.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/transition/webView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin-app/pages/transition/webView.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/FileView/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/FileView/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/FormListDialog/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/FormListDialog/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/ScollSelect/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/ScollSelect/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/StContract/AgreementDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/components/StContract/AgreementDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/ApplicationProgressDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/ApplicationProgressDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/StMaterialCollectDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/StMaterialCollectDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/components/EmailRecordList.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/components/EmailRecordList.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/components/StepOperationForm.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/components/StepOperationForm.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/components/stepForms/UnifiedStepForm.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../nuodun-ui/nuodun-admin/src/views/tran/stMaterialPlan/components/stepForms/UnifiedStepForm.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-admin/src/main/java/com/nuodun/web/controller/tran/TStMaterialPlanProgressController.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-admin/src/main/java/com/nuodun/web/controller/tran/TStMaterialPlanProgressController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-app/src/main/java/com/nuodun/web/controller/quzrOpen/OpenWxController.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-app/src/main/java/com/nuodun/web/controller/quzrOpen/OpenWxController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-common/src/main/java/com/nuodun/common/enums/MaterialPlanProgressEnum.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-common/src/main/java/com/nuodun/common/enums/MaterialPlanProgressEnum.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/domain/TStMaterialPlanProgress.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/domain/TStMaterialPlanProgress.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/TStMaterialPlanProgressService.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/TStMaterialPlanProgressService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TFamServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TFamServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStContractAgreementServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStContractAgreementServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStMaterialPlanProgressServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStMaterialPlanProgressServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TTeReminderUserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TTeReminderUserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/weixin/service/WxUserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/weixin/service/WxUserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/weixin/service/impl/WxUserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/weixin/service/impl/WxUserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nuodun-system/src/main/resources/mapper/tran/TStMaterialPlanProgressMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/nuodun-system/src/main/resources/mapper/tran/TStMaterialPlanProgressMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\maven\apache-maven-3.8.8" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2u9N9h78soei64CwUW653OPpePx" />
  <component name="ProjectViewState">
    <option name="flattenModules" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ApiPost:METDOD_SEND_RECORD:joolun-open&quot;: &quot;{\&quot;/quzr-open/aly/getVerCode\&quot;:[{\&quot;url\&quot;:\&quot;http://localhost:7002/quzr-open/aly/getVerCode\&quot;,\&quot;header\&quot;:[],\&quot;query\&quot;:[{\&quot;key\&quot;:\&quot;phone\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;},{\&quot;key\&quot;:\&quot;type\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;type\&quot;:\&quot;Text\&quot;,\&quot;description\&quot;:\&quot;\&quot;,\&quot;is_checked\&quot;:1,\&quot;not_null\&quot;:\&quot;0\&quot;,\&quot;field_type\&quot;:\&quot;String\&quot;}],\&quot;rest\&quot;:[],\&quot;cookie\&quot;:[],\&quot;body\&quot;:{\&quot;mode\&quot;:\&quot;none\&quot;,\&quot;parameter\&quot;:[],\&quot;raw\&quot;:\&quot;\&quot;,\&quot;raw_para\&quot;:[]},\&quot;responseBody\&quot;:\&quot;{\\\&quot;msg\\\&quot;:\\\&quot;鎵嬫満鍙蜂笉鑳戒负绌�\\\&quot;,\\\&quot;code\\\&quot;:500}\&quot;,\&quot;responseHeader\&quot;:{\&quot;Keep-Alive\&quot;:[\&quot;timeout\\u003d60\&quot;],\&quot;Transfer-Encoding\&quot;:[\&quot;chunked\&quot;],\&quot;X-Frame-Options\&quot;:[\&quot;SAMEORIGIN\&quot;],\&quot;X-Content-Type-Options\&quot;:[\&quot;nosniff\&quot;],\&quot;Connection\&quot;:[\&quot;keep-alive\&quot;],\&quot;Vary\&quot;:[\&quot;Origin\&quot;,\&quot;Access-Control-Request-Method\&quot;,\&quot;Access-Control-Request-Headers\&quot;],\&quot;X-XSS-Protection\&quot;:[\&quot;0\&quot;],\&quot;Date\&quot;:[\&quot;Mon, 17 Mar 2025 12:44:24 GMT\&quot;],\&quot;Content-Type\&quot;:[\&quot;application/json\&quot;]},\&quot;responseCookieList\&quot;:[],\&quot;selectedItem\&quot;:\&quot;GET\&quot;,\&quot;time\&quot;:{\&quot;date\&quot;:{\&quot;year\&quot;:2025,\&quot;month\&quot;:3,\&quot;day\&quot;:17},\&quot;time\&quot;:{\&quot;hour\&quot;:20,\&quot;minute\&quot;:44,\&quot;second\&quot;:24,\&quot;nano\&quot;:232135600}}}]}&quot;,
    &quot;Application.AesUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.DesensitizedUtil.executor&quot;: &quot;Run&quot;,
    &quot;Application.EscapeUtil.executor&quot;: &quot;Run&quot;,
    &quot;Application.PdfUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.TTeReminderUserServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;Application.TemplatePdfService.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.StContractTest.settleContractCommission.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.WxMpTemplateMsgServiceImplTest.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.WxMpTemplateMsgServiceImplTest.testSendTemplateMsg.executor&quot;: &quot;Run&quot;,
    &quot;Maven.joolun-wx [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.nuodun [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.nuodun [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.nuodun [validate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.JooLunAdminApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.JooLunAppApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.JooLunApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.JooLunOpenApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.NuodunAdminApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.NuodunAppApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/work/gitData/nuodun-boot/nuodun&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-system\src\main\java\com\joolun\framework\web\service" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-system\src\main\java\com\joolun\framework" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-system\src\main\resources" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-app\src\main\java\com\joolun\web\controller\system" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-admin\src\main\java\com\joolun\web\controller" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\work\gitData\nuodun-boot\nuodun\nuodun-system\src\main\resources\mapper\tran" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-system\src\main\resources\mapper\tran" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-system\src\main\resources\mapper\system" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-system\src\main\resources" />
      <recent name="D:\work\gitData\nuodun-joolun\nuodun\joolun-system\src\main\resources\sql" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.nuodun.tran.domain.entity" />
      <recent name="com.nuodun.tran.service.impl.message.handlers" />
      <recent name="com.nuodun.web.controller.tran" />
      <recent name="com.nuodun.tran.domain.dto" />
      <recent name="com.nuodun.tran.domain.param" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.NuodunAppApplication">
    <configuration name="EscapeUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.nuodun.common.utils.html.EscapeUtil" />
      <module name="nuodun-common" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.nuodun.common.utils.html.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PdfUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.nuodun.common.utils.file.PdfUtils" />
      <module name="nuodun-common" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.nuodun.common.utils.file.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TemplatePdfService" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.nuodun.framework.web.service.TemplatePdfService" />
      <module name="nuodun-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.nuodun.framework.web.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="NuodunAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="nuodun-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nuodun.NuodunAdminApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.nuodun.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="NuodunAppApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="nuodun-app" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nuodun.NuodunAppApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.nuodun.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.EscapeUtil" />
      <item itemvalue="Application.PdfUtils" />
      <item itemvalue="Application.TemplatePdfService" />
      <item itemvalue="Spring Boot.NuodunAdminApplication" />
      <item itemvalue="Spring Boot.NuodunAppApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.NuodunAdminApplication" />
        <item itemvalue="Spring Boot.NuodunAppApplication" />
        <item itemvalue="Application.EscapeUtil" />
        <item itemvalue="Application.TemplatePdfService" />
        <item itemvalue="Application.PdfUtils" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9145f901-5d7e-46c6-9112-0ef457831d44" name="Changes" comment="" />
      <created>1741223405520</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741223405520</updated>
      <workItem from="1741223408791" duration="732000" />
      <workItem from="1741656682246" duration="8181000" />
      <workItem from="1741678908081" duration="22915000" />
      <workItem from="1741765296295" duration="7411000" />
      <workItem from="1741826585884" duration="81094000" />
      <workItem from="1742355888166" duration="58931000" />
      <workItem from="1742529821879" duration="195785000" />
      <workItem from="1743593681079" duration="86955000" />
      <workItem from="1744159786213" duration="200737000" />
      <workItem from="1745208515078" duration="24830000" />
      <workItem from="1745297881088" duration="75076000" />
      <workItem from="1745732276722" duration="73280000" />
      <workItem from="1745991944029" duration="2101000" />
      <workItem from="1745997234814" duration="22983000" />
      <workItem from="1746579301108" duration="56000" />
      <workItem from="1746579367446" duration="21000" />
      <workItem from="1746579396105" duration="94875000" />
      <workItem from="1747184269600" duration="53151000" />
      <workItem from="1747635137043" duration="248671000" />
      <workItem from="1749603497349" duration="241631000" />
      <workItem from="1750834581047" duration="150150000" />
      <workItem from="1751538679635" duration="50038000" />
      <workItem from="1751936871538" duration="56884000" />
      <workItem from="1752109289626" duration="78738000" />
      <workItem from="1752548784673" duration="67788000" />
      <workItem from="1752803139819" duration="9153000" />
      <workItem from="1752821022510" duration="14379000" />
      <workItem from="1753069759086" duration="48732000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/joolun-framework/src/main/java/com/joolun/framework/config/MybatisPlusConfig.java</url>
          <line>25</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>jar://$PROJECT_DIR$/../../../../maven/repository/com/alibaba/easyexcel-core/3.1.1/easyexcel-core-3.1.1.jar!/com/alibaba/excel/EasyExcelFactory.class</url>
          <line>44</line>
          <option name="timeStamp" value="188" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-common/src/main/java/com/nuodun/common/config/typehandler/JsonArrayTypeHandler.java</url>
          <line>47</line>
          <option name="timeStamp" value="211" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-common/src/main/java/com/nuodun/common/config/typehandler/JsonArrayTypeHandler.java</url>
          <line>40</line>
          <option name="timeStamp" value="212" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-admin/src/test/java/com/nuodun/WxMpTemplateMsgServiceImplTest.java</url>
          <line>48</line>
          <option name="timeStamp" value="309" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-admin/src/test/java/com/nuodun/WxMpTemplateMsgServiceImplTest.java</url>
          <line>41</line>
          <option name="timeStamp" value="310" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStContractCommissionRecordServiceImpl.java</url>
          <line>70</line>
          <option name="timeStamp" value="322" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStContractCommissionRecordServiceImpl.java</url>
          <line>466</line>
          <option name="timeStamp" value="328" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStContractCommissionRecordServiceImpl.java</url>
          <line>501</line>
          <option name="timeStamp" value="329" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TStContractCommissionRecordServiceImpl.java</url>
          <line>446</line>
          <option name="timeStamp" value="331" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-common/src/main/java/com/nuodun/common/core/service/impl/OssFileUploadServiceImpl.java</url>
          <line>82</line>
          <option name="timeStamp" value="408" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-common/src/main/java/com/nuodun/common/core/redis/RedisCache.java</url>
          <line>141</line>
          <option name="timeStamp" value="480" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/tran/service/impl/TTeReminderServiceImpl.java</url>
          <line>478</line>
          <option name="timeStamp" value="512" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-quartz/src/main/java/com/nuodun/quartz/task/NoticeTask.java</url>
          <line>114</line>
          <option name="timeStamp" value="543" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/nuodun-system/src/main/java/com/nuodun/weixin/config/WxMaConfiguration.java</url>
          <line>42</line>
          <option name="timeStamp" value="545" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="JUnit">
        <watch expression="thirdPartyFee" />
      </configuration>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="param" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>