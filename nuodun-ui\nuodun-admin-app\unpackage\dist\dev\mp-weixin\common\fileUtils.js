"use strict";const e=require("./vendor.js"),o=require("./toast.js"),n=require("./request.js");function s(n){return new Promise(((s,i)=>{let t=n.sourceType;"album"===n.fileSourceType?t=["album"]:"chat"===n.fileSourceType&&(t=["album","camera"]),e.index.chooseImage({count:n.count,sizeType:["compressed"],sourceType:t,success:e=>{const t=e.tempFilePaths.map(((o,n)=>{const s=e.tempFiles[n];return{name:s.name||`image_${Date.now()}_${n}.jpg`,size:s.size||0,type:s.type||"image/jpeg",path:o}})).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==t.length?s(t):i(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:618","选择图片失败:",o),i(new Error("选择图片失败"))}})}))}function i(n){return new Promise(((s,i)=>{e.index.chooseMessageFile({count:n.count,type:"all",success:e=>{const t=e.tempFiles.map((e=>({name:e.name,size:e.size,type:e.type||"",path:e.path}))).filter((e=>!(e.size>1024*n.maxSize*1024)||(o.toast.show(`文件 ${e.name} 超过 ${n.maxSize}MB 限制`),!1)));0!==t.length?s(t):i(new Error("没有有效的文件"))},fail:o=>{e.index.__f__("error","at common/fileUtils.js:657","选择文件失败:",o),i(new Error("选择文件失败"))}})}))}exports.chooseAndUploadFile=function(o={}){const t={count:1,maxSize:10,allowedTypes:["image","file"],acceptTypes:null,sourceType:["album","camera"],fileSourceType:"chat",uploadData:{fileBizType:"common_file_upload"},uploadUrl:"/system/fileRecord/uploadByFile",onChoose:null,onProgress:null,onSuccess:null,onFail:null,onComplete:null,autoUpload:!0,showLoading:!0,...o};return new Promise(((o,l)=>{let r=null;if(t.allowedTypes.includes("image"),t.allowedTypes.some((e=>"image"!==e)),r="album"===t.fileSourceType?s:i,!r){const e=new Error("当前平台不支持文件选择");return t.onFail&&t.onFail(e),t.onComplete&&t.onComplete(!1),void l(e)}r(t).then((s=>{if(t.onChoose){if(!1===t.onChoose(s))return void o({files:s,uploaded:!1})}t.autoUpload?function(o,s){return new Promise(((i,t)=>{if(!o||0===o.length)return void t(new Error("没有文件需要上传"));const l=o.map(((o,i)=>function(o,s,i){return new Promise(((t,l)=>{s.showLoading&&e.index.showLoading({title:`上传中... (${i+1})`});const r={url:s.uploadUrl,filePath:o.path,name:"file",formData:s.uploadData,success:n=>{if(s.showLoading&&e.index.hideLoading(),200===n.code&&n.data){const e={...o,...n.data,uploaded:!0,uploadTime:(new Date).getTime()};s.onProgress&&s.onProgress({file:e,index:i,progress:100,status:"success"}),t(e)}else{const e=new Error(n.msg||"上传失败");e.file=o,e.index=i,s.onProgress&&s.onProgress({file:o,index:i,progress:0,status:"error",error:e}),l(e)}},fail:n=>{s.showLoading&&e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:753","上传文件失败:",n);const t=new Error("上传失败");t.file=o,t.index=i,t.originalError=n,s.onProgress&&s.onProgress({file:o,index:i,progress:0,status:"error",error:t}),l(t)}},a=n.upload(r);a&&a.onProgressUpdate&&s.onProgress&&a.onProgressUpdate((e=>{s.onProgress({file:o,index:i,progress:e.progress,status:"uploading"})}))}))}(o,s,i)));Promise.all(l).then((e=>{s.onSuccess&&s.onSuccess(e),s.onComplete&&s.onComplete(!0),i(e)})).catch((e=>{s.onFail&&s.onFail(e),s.onComplete&&s.onComplete(!1),t(e)}))}))}(s,t).then((e=>{o({files:s,uploadResults:e,uploaded:!0})})).catch((e=>{t.onFail&&t.onFail(e),t.onComplete&&t.onComplete(!1),l(e)})):o({files:s,uploaded:!1})})).catch((e=>{t.onFail&&t.onFail(e),t.onComplete&&t.onComplete(!1),l(e)}))}))},exports.downloadAndOpenFile=function(n,s={}){const i={onSuccess:null,onFail:null,onComplete:null,loadingText:"文件下载中...",successText:"文件已保存",failText:"文件下载失败",autoOpen:!0,fileName:"",...s};return new Promise(((s,t)=>{if(!n){const e=new Error("没有可下载的文件");return i.onFail&&i.onFail(e),i.onComplete&&i.onComplete(!1),void t(e)}e.index.showLoading(i.loadingText),e.index.downloadFile({url:n,success:n=>{200===n.statusCode?e.index.saveFile({tempFilePath:n.tempFilePath,success:n=>{e.index.hideLoading(),i.successText&&o.toast.show(i.successText),i.autoOpen?e.index.openDocument({filePath:n.savedFilePath,success:()=>{i.onSuccess&&i.onSuccess(n),i.onComplete&&i.onComplete(!0),s(n)},fail:e=>{o.toast.show("无法打开此类型文件"),i.onSuccess&&i.onSuccess(n),i.onComplete&&i.onComplete(!0),s(n)}}):(i.onSuccess&&i.onSuccess(n),i.onComplete&&i.onComplete(!0),s(n))},fail:n=>{e.index.hideLoading(),o.toast.show("文件保存失败"),i.onFail&&i.onFail(n),i.onComplete&&i.onComplete(!1),t(n)}}):(e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:960","文件下载失败",n),o.toast.show(i.failText),i.onFail&&i.onFail(n),i.onComplete&&i.onComplete(!1),t(new Error(i.failText)))},fail:n=>{e.index.hideLoading(),e.index.__f__("error","at common/fileUtils.js:971","文件下载失败",n),o.toast.show(i.failText),i.onFail&&i.onFail(n),i.onComplete&&i.onComplete(!1),t(n)}})}))},exports.getFileType=function(e){if(!e)return"file";const o=e.toLowerCase().split(".").pop();return["jpg","jpeg","png","gif","bmp","webp"].includes(o)?"image":["mp4","avi","mov","wmv","flv","3gp"].includes(o)?"video":["pdf","doc","docx","xls","xlsx","ppt","pptx","txt"].includes(o)?"document":"file"},exports.getFileTypeIcon=function(e){if(!e)return"📄";const o=e.toLowerCase().split(".").pop();return["jpg","jpeg","png","gif","bmp","webp"].includes(o)?"🖼️":"pdf"===o?"📕":["doc","docx"].includes(o)?"📘":["xls","xlsx"].includes(o)?"📗":["ppt","pptx"].includes(o)?"📙":"📄"},exports.validateMaterialFileType=function(o,n=null){const s=function(o,n=null){if(o&&o.includes(".")){const n=o.toLowerCase().split(".").pop();return e.index.__f__("log","at common/fileUtils.js:219","从文件名获取扩展名:",{fileName:o,ext:n}),n}if(n&&n.includes(".")){const o=n.toLowerCase().split(".").pop();return e.index.__f__("log","at common/fileUtils.js:226","从文件路径获取扩展名:",{filePath:n,ext:o}),o}return e.index.__f__("log","at common/fileUtils.js:230","无法获取文件扩展名:",{fileName:o,filePath:n}),null}(o,n);return!!s&&["jpg","jpeg","png","gif","bmp","webp","pdf","doc","docx","xls","xlsx","ppt","pptx"].includes(s)};
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/fileUtils.js.map
