import {toast} from "@/common/toast.js"
import {upload} from "@/common/request"

/**
 * 通用文件选择和上传工具类
 * 支持H5、APP、小程序多端
 */

/**
 * 通用文件选择和上传方法
 * 支持H5、APP、小程序多端
 * @param {Object} options 配置选项
 * @param {Number} options.count 选择文件数量，默认1
 * @param {Number} options.maxSize 文件大小限制(MB)，默认10MB
 * @param {Array} options.allowedTypes 允许的文件类型，如['image', 'video', 'file', 'document']，默认['image', 'file']
 * @param {String} options.acceptTypes H5环境下自定义accept属性，如'image/*,.pdf,.doc,.docx'
 * @param {String} options.sourceType 选择来源，如['album', 'camera']，默认['album', 'camera']
 * @param {String} options.fileSourceType 微信小程序文件来源类型，'album'从相册选择，'chat'从聊天记录选择
 * @param {Object} options.uploadData 上传时的额外数据，默认{ fileBizType: 'common_file_upload' }
 * @param {String} options.uploadUrl 上传接口地址，默认'/system/fileRecord/uploadByFile'
 * @param {Function} options.onChoose 选择文件后的回调，可用于预处理
 * @param {Function} options.onProgress 上传进度回调
 * @param {Function} options.onSuccess 上传成功回调
 * @param {Function} options.onFail 上传失败回调
 * @param {Function} options.onComplete 上传完成回调（无论成功失败）
 * @param {Boolean} options.autoUpload 是否选择后自动上传，默认true
 * @param {Boolean} options.showLoading 是否显示加载提示，默认true
 * @returns {Promise} 返回Promise，resolve时包含选择的文件信息和上传结果
 */
export function chooseAndUploadFile(options = {}) {
    // 默认配置
    const defaultOptions = {
        count: 1,
        maxSize: 10, // MB
        allowedTypes: ['image', 'file'],
        acceptTypes: null, // H5环境下自定义accept属性
        sourceType: ['album', 'camera'],
        fileSourceType: 'chat', // 微信小程序默认从聊天记录选择
        uploadData: {fileBizType: 'common_file_upload'},
        uploadUrl: '/system/fileRecord/uploadByFile',
        onChoose: null,
        onProgress: null,
        onSuccess: null,
        onFail: null,
        onComplete: null,
        autoUpload: true,
        showLoading: true
    };

    // 合并配置
    const opts = {...defaultOptions, ...options};

    return new Promise((resolve, reject) => {
        // 根据平台选择不同的文件选择方式
        let chooseMethod = null;

        // 判断是否包含图片类型
        const hasImage = opts.allowedTypes.includes('image');
        // 判断是否包含其他文件类型
        const hasOtherFiles = opts.allowedTypes.some(type => type !== 'image');

        // #ifdef H5
        // H5环境需要检测是否在微信浏览器中
        const isWechatBrowser = /micromessenger/i.test(navigator.userAgent);
        if (isWechatBrowser) {
            // 微信浏览器环境，使用特殊处理
            chooseMethod = chooseFileWechat;
        } else {
            // 普通H5环境使用input file
            chooseMethod = chooseFileH5;
        }
        // #endif

        // #ifdef APP-PLUS
        // APP环境根据文件类型选择方法
        if (hasImage && !hasOtherFiles) {
            // 只选择图片
            chooseMethod = chooseImageApp;
        } else if (hasOtherFiles) {
            // 选择文件（包括图片）
            chooseMethod = chooseFileApp;
        } else {
            chooseMethod = chooseImageApp;
        }
        // #endif

        // #ifdef MP
        // 小程序环境
        if (hasImage && !hasOtherFiles && opts.fileSourceType === 'album') {
            // 只选择图片且从相册选择
            chooseMethod = chooseImageMp;
        } else if (opts.fileSourceType === 'album') {
            // 从相册选择文件（包括图片）
            chooseMethod = chooseFileFromAlbumMp;
        } else {
            // 从聊天记录选择文件（默认行为）
            chooseMethod = chooseFileMp;
        }
        // #endif

        if (!chooseMethod) {
            const error = new Error('当前平台不支持文件选择');
            if (opts.onFail) opts.onFail(error);
            if (opts.onComplete) opts.onComplete(false);
            reject(error);
            return;
        }

        // 执行文件选择
        chooseMethod(opts)
            .then(files => {
                // 文件选择成功，执行选择回调
                if (opts.onChoose) {
                    const result = opts.onChoose(files);
                    if (result === false) {
                        // 如果回调返回false，取消后续操作
                        resolve({files, uploaded: false});
                        return;
                    }
                }

                // 如果不自动上传，直接返回文件信息
                if (!opts.autoUpload) {
                    resolve({files, uploaded: false});
                    return;
                }

                // 自动上传文件
                uploadFiles(files, opts)
                    .then(uploadResults => {
                        resolve({files, uploadResults, uploaded: true});
                    })
                    .catch(uploadError => {
                        if (opts.onFail) opts.onFail(uploadError);
                        if (opts.onComplete) opts.onComplete(false);
                        reject(uploadError);
                    });
            })
            .catch(chooseError => {
                if (opts.onFail) opts.onFail(chooseError);
                if (opts.onComplete) opts.onComplete(false);
                reject(chooseError);
            });
    });
}

/**
 * 格式化文件大小
 * @param {Number} size 文件大小（字节）
 * @returns {String} 格式化后的文件大小
 */
export function formatFileSize(size) {
    if (!size || size === 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    let index = 0;
    let fileSize = size;

    while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024;
        index++;
    }

    return `${fileSize.toFixed(index === 0 ? 0 : 1)} ${units[index]}`;
}

/**
 * 验证文件类型
 * @param {String} fileName 文件名
 * @param {Array} allowedTypes 允许的文件类型
 * @returns {Boolean} 是否允许
 */
export function validateFileType(fileName, allowedTypes = []) {
    if (!fileName || allowedTypes.length === 0) return true;

    const ext = fileName.toLowerCase().split('.').pop();
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', '3gp'];
    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

    for (const type of allowedTypes) {
        if (type === 'image' && imageTypes.includes(ext)) return true;
        if (type === 'video' && videoTypes.includes(ext)) return true;
        if (type === 'document' && documentTypes.includes(ext)) return true;
        if (type === 'file') return true; // 允许所有文件类型
    }

    return false;
}

/**
 * 验证文件类型
 * @param {String} fileName 文件名
 * @param {Array} allowedTypes 允许的文件类型
 * @returns {Boolean} 是否允许
 */
export function getFileType(fileName) {
    if (!fileName) return 'file';

    const ext = fileName.toLowerCase().split('.').pop();
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', '3gp'];
    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

    if (imageTypes.includes(ext)) return 'image';
    if (videoTypes.includes(ext)) return 'video';
    if (documentTypes.includes(ext)) return 'document';

    return 'file';
}

/**
 * 从文件名或路径中提取文件扩展名
 * @param {String} fileName 文件名
 * @param {String} filePath 文件路径（可选）
 * @returns {String|null} 文件扩展名，如果无法获取则返回null
 */
export function getFileExtension(fileName, filePath = null) {
    // 优先从 fileName 获取扩展名
    if (fileName && fileName.includes('.')) {
        const ext = fileName.toLowerCase().split('.').pop()
        console.log('从文件名获取扩展名:', {fileName, ext})
        return ext
    }

    // 如果 fileName 没有扩展名，从 filePath 获取
    if (filePath && filePath.includes('.')) {
        const ext = filePath.toLowerCase().split('.').pop()
        console.log('从文件路径获取扩展名:', {filePath, ext})
        return ext
    }

    console.log('无法获取文件扩展名:', {fileName, filePath})
    return null
}

/**
 * 验证材料文件类型
 * @param {String} fileName 文件名
 * @param {String} filePath 文件路径（可选）
 * @returns {Boolean} 是否为允许的文件类型
 */
export function validateMaterialFileType(fileName, filePath = null) {
    const ext = getFileExtension(fileName, filePath)
    if (!ext) return false

    const allowedExtensions = [
        // 图片格式
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
        // 文档格式
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
    ]

    return allowedExtensions.includes(ext)
}

/**
 * 获取文件类型图标
 * @param {String} fileName 文件名
 * @returns {String} 文件类型图标
 */
export function getFileTypeIcon(fileName) {
    if (!fileName) return '📄';

    const ext = fileName.toLowerCase().split('.').pop();

    // 图片类型
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
        return '🖼️';
    }

    // PDF
    if (ext === 'pdf') {
        return '📕';
    }

    // Word文档
    if (['doc', 'docx'].includes(ext)) {
        return '📘';
    }

    // Excel表格
    if (['xls', 'xlsx'].includes(ext)) {
        return '📗';
    }

    // PowerPoint
    if (['ppt', 'pptx'].includes(ext)) {
        return '📙';
    }

    // 其他文件
    return '📄';
}

// 微信浏览器环境文件选择
function chooseFileWechat(opts) {
    return new Promise((resolve, reject) => {
        console.log('微信浏览器文件选择开始', opts);

        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = opts.count > 1;

        // 微信浏览器对accept属性支持有限，需要简化
        if (opts.acceptTypes) {
            input.accept = opts.acceptTypes;
            console.log('使用自定义accept:', opts.acceptTypes);
        } else {
            // 微信浏览器中，简化accept属性
            const acceptTypes = [];
            if (opts.allowedTypes.includes('image')) {
                acceptTypes.push('image/*');
            }
            if (opts.allowedTypes.includes('document') || opts.allowedTypes.includes('file')) {
                // 微信浏览器中，使用通用的文件类型
                acceptTypes.push('*/*');
            }
            input.accept = acceptTypes.join(',');
            console.log('生成的accept属性:', input.accept);
        }

        // 添加样式确保在微信中可见
        input.style.position = 'absolute';
        input.style.left = '-9999px';
        input.style.opacity = '0';
        input.style.zIndex = '9999';
        document.body.appendChild(input);

        console.log('input元素已创建并添加到DOM');

        input.onchange = (e) => {
            console.log('文件选择change事件触发', e.target.files);
            const files = Array.from(e.target.files);

            // 验证文件
            const validFiles = [];
            for (const file of files) {
                console.log('处理文件:', file.name, file.size, file.type);

                if (file.size > opts.maxSize * 1024 * 1024) {
                    console.log('文件大小超限:', file.name);
                    uni.showToast({
                        title: `文件 ${file.name} 超过 ${opts.maxSize}MB 限制`,
                        icon: 'none'
                    });
                    continue;
                }

                // 在微信浏览器中验证文件类型
                if (!validateWechatFileType(file.name, opts.allowedTypes)) {
                    console.log('文件类型不支持:', file.name);
                    uni.showToast({
                        title: `文件 ${file.name} 格式不支持`,
                        icon: 'none'
                    });
                    continue;
                }

                validFiles.push({
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    // #ifdef H5
                    path: URL.createObjectURL(file),
                    // #endif
                    // #ifndef H5
                    path: file.path || '',
                    // #endif
                    file: file
                });
            }

            // 清理DOM
            try {
                document.body.removeChild(input);
            } catch (err) {
                console.warn('清理input元素失败:', err);
            }

            console.log('有效文件数量:', validFiles.length);
            if (validFiles.length === 0) {
                reject(new Error('没有有效的文件'));
                return;
            }

            resolve(validFiles);
        };

        // 添加错误处理
        input.onerror = (err) => {
            console.error('文件选择错误:', err);
            try {
                document.body.removeChild(input);
            } catch (e) {
                console.warn('清理input元素失败:', e);
            }
            reject(new Error('文件选择失败'));
        };

        // 触发文件选择
        console.log('准备触发文件选择');
        setTimeout(() => {
            try {
                input.click();
                console.log('文件选择已触发');
            } catch (err) {
                console.error('触发文件选择失败:', err);
                reject(new Error('无法打开文件选择器'));
            }
        }, 100);
    });
}

// 验证微信浏览器中的文件类型
function validateWechatFileType(fileName, allowedTypes) {
    if (!fileName || allowedTypes.length === 0) return true;

    const ext = fileName.toLowerCase().split('.').pop();
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx'];

    for (const type of allowedTypes) {
        if (type === 'image' && imageTypes.includes(ext)) return true;
        if ((type === 'document' || type === 'file') && documentTypes.includes(ext)) return true;
    }

    return false;
}

// H5环境文件选择
function chooseFileH5(opts) {
    return new Promise((resolve, reject) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = opts.count > 1;

        // 设置接受的文件类型
        if (opts.acceptTypes) {
            // 如果指定了自定义accept类型，直接使用
            input.accept = opts.acceptTypes;
        } else {
            // 否则根据allowedTypes生成accept属性
            const acceptTypes = [];
            if (opts.allowedTypes.includes('image')) {
                acceptTypes.push('image/*');
            }
            if (opts.allowedTypes.includes('video')) {
                acceptTypes.push('video/*');
            }
            if (opts.allowedTypes.includes('document')) {
                // 支持常见的办公文档格式
                acceptTypes.push('.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx');
            }
            if (opts.allowedTypes.includes('file')) {
                acceptTypes.push('.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar');
            }
            input.accept = acceptTypes.join(',');
        }

        input.onchange = (e) => {
            const files = Array.from(e.target.files);

            // 验证文件
            const validFiles = [];
            for (const file of files) {
                if (file.size > opts.maxSize * 1024 * 1024) {
                    toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);
                    continue;
                }
                validFiles.push({
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    // #ifdef H5
                    path: URL.createObjectURL(file),
                    // #endif
                    // #ifndef H5
                    path: file.path || '',
                    // #endif
                    file: file // H5环境保存原始文件对象
                });
            }

            if (validFiles.length === 0) {
                reject(new Error('没有有效的文件'));
                return;
            }

            resolve(validFiles);
        };

        input.click();
    });
}

// APP环境图片选择
function chooseImageApp(opts) {
    return new Promise((resolve, reject) => {
        uni.chooseImage({
            count: opts.count,
            sizeType: ['compressed'],
            sourceType: opts.sourceType,
            success: (res) => {
                const files = res.tempFilePaths.map((path, index) => {
                    const file = res.tempFiles[index];
                    return {
                        name: file.name || `image_${Date.now()}_${index}.jpg`,
                        size: file.size || 0,
                        type: file.type || 'image/jpeg',
                        path: path
                    };
                });

                // 验证文件大小
                const validFiles = files.filter(file => {
                    if (file.size > opts.maxSize * 1024 * 1024) {
                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);
                        return false;
                    }
                    return true;
                });

                if (validFiles.length === 0) {
                    reject(new Error('没有有效的文件'));
                    return;
                }

                resolve(validFiles);
            },
            fail: (err) => {
                console.error('选择图片失败:', err);
                reject(new Error('选择图片失败'));
            }
        });
    });
}

// APP环境文件选择
function chooseFileApp(opts) {
    return new Promise((resolve, reject) => {
        // APP环境使用 chooseMessageFile 选择文件
        uni.chooseMessageFile({
            count: opts.count,
            type: 'all',
            success: (res) => {
                const files = res.tempFiles.map(file => ({
                    name: file.name,
                    size: file.size,
                    type: file.type || '',
                    path: file.path
                }));

                // 验证文件大小
                const validFiles = files.filter(file => {
                    if (file.size > opts.maxSize * 1024 * 1024) {
                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);
                        return false;
                    }
                    return true;
                });

                if (validFiles.length === 0) {
                    reject(new Error('没有有效的文件'));
                    return;
                }

                resolve(validFiles);
            },
            fail: (err) => {
                console.error('选择文件失败:', err);
                reject(new Error('选择文件失败'));
            }
        });
    });
}

// 小程序环境图片选择
function chooseImageMp(opts) {
    return new Promise((resolve, reject) => {
        uni.chooseImage({
            count: opts.count,
            sizeType: ['compressed'],
            sourceType: opts.sourceType,
            success: (res) => {
                const files = res.tempFilePaths.map((path, index) => {
                    const file = res.tempFiles[index];
                    return {
                        name: file.name || `image_${Date.now()}_${index}.jpg`,
                        size: file.size || 0,
                        type: file.type || 'image/jpeg',
                        path: path
                    };
                });

                // 验证文件大小
                const validFiles = files.filter(file => {
                    if (file.size > opts.maxSize * 1024 * 1024) {
                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);
                        return false;
                    }
                    return true;
                });

                if (validFiles.length === 0) {
                    reject(new Error('没有有效的文件'));
                    return;
                }

                resolve(validFiles);
            },
            fail: (err) => {
                console.error('选择图片失败:', err);
                reject(new Error('选择图片失败'));
            }
        });
    });
}

// 小程序环境从相册选择文件
function chooseFileFromAlbumMp(opts) {
    return new Promise((resolve, reject) => {
        // 使用 chooseImage 从相册选择图片
        uni.chooseImage({
            count: opts.count,
            sizeType: ['compressed', 'original'],
            sourceType: ['album'], // 只从相册选择
            success: (res) => {
                const files = res.tempFilePaths.map((path, index) => {
                    const file = res.tempFiles[index];
                    return {
                        name: file.name || `image_${Date.now()}_${index}.jpg`,
                        size: file.size || 0,
                        type: file.type || 'image/jpeg',
                        path: path
                    };
                });

                // 验证文件大小
                const validFiles = files.filter(file => {
                    if (file.size > opts.maxSize * 1024 * 1024) {
                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);
                        return false;
                    }
                    return true;
                });

                if (validFiles.length === 0) {
                    reject(new Error('没有有效的文件'));
                    return;
                }

                resolve(validFiles);
            },
            fail: (err) => {
                console.error('从相册选择文件失败:', err);
                reject(new Error('从相册选择文件失败'));
            }
        });
    });
}

// 小程序环境文件选择（从聊天记录）
function chooseFileMp(opts) {
    return new Promise((resolve, reject) => {
        // 小程序环境使用 chooseMessageFile
        uni.chooseMessageFile({
            count: opts.count,
            type: 'all',
            success: (res) => {
                const files = res.tempFiles.map(file => ({
                    name: file.name,
                    size: file.size,
                    type: file.type || '',
                    path: file.path
                }));

                // 验证文件大小
                const validFiles = files.filter(file => {
                    if (file.size > opts.maxSize * 1024 * 1024) {
                        toast.show(`文件 ${file.name} 超过 ${opts.maxSize}MB 限制`);
                        return false;
                    }
                    return true;
                });

                if (validFiles.length === 0) {
                    reject(new Error('没有有效的文件'));
                    return;
                }

                resolve(validFiles);
            },
            fail: (err) => {
                console.error('选择文件失败:', err);
                reject(new Error('选择文件失败'));
            }
        });
    });
}

// 上传文件
function uploadFiles(files, opts) {
    return new Promise((resolve, reject) => {
        if (!files || files.length === 0) {
            reject(new Error('没有文件需要上传'));
            return;
        }

        const uploadPromises = files.map((file, index) => {
            return uploadSingleFile(file, opts, index);
        });

        Promise.all(uploadPromises)
            .then(results => {
                if (opts.onSuccess) opts.onSuccess(results);
                if (opts.onComplete) opts.onComplete(true);
                resolve(results);
            })
            .catch(error => {
                if (opts.onFail) opts.onFail(error);
                if (opts.onComplete) opts.onComplete(false);
                reject(error);
            });
    });
}

// 上传单个文件
function uploadSingleFile(file, opts, index) {
    return new Promise((resolve, reject) => {
        if (opts.showLoading) {
            uni.showLoading({
                title: `上传中... (${index + 1})`
            });
        }

        // 准备上传参数
        const uploadOptions = {
            url: opts.uploadUrl,
            filePath: file.path,
            name: 'file',
            formData: opts.uploadData,
            success: (res) => {
                if (opts.showLoading) {
                    uni.hideLoading();
                }

                if (res.code === 200 && res.data) {
                    // 上传成功
                    const result = {
                        ...file,
                        ...res.data,
                        uploaded: true,
                        uploadTime: new Date().getTime()
                    };

                    if (opts.onProgress) {
                        opts.onProgress({
                            file: result,
                            index,
                            progress: 100,
                            status: 'success'
                        });
                    }

                    resolve(result);
                } else {
                    // 上传失败
                    const error = new Error(res.msg || '上传失败');
                    error.file = file;
                    error.index = index;

                    if (opts.onProgress) {
                        opts.onProgress({
                            file,
                            index,
                            progress: 0,
                            status: 'error',
                            error
                        });
                    }

                    reject(error);
                }
            },
            fail: (err) => {
                if (opts.showLoading) {
                    uni.hideLoading();
                }

                console.error('上传文件失败:', err);
                const error = new Error('上传失败');
                error.file = file;
                error.index = index;
                error.originalError = err;

                if (opts.onProgress) {
                    opts.onProgress({
                        file,
                        index,
                        progress: 0,
                        status: 'error',
                        error
                    });
                }

                reject(error);
            }
        };

        // 执行上传
        const uploadTask = upload(uploadOptions);

        // 监听上传进度
        if (uploadTask && uploadTask.onProgressUpdate && opts.onProgress) {
            uploadTask.onProgressUpdate((progressRes) => {
                opts.onProgress({
                    file,
                    index,
                    progress: progressRes.progress,
                    status: 'uploading'
                });
            });
        }
    });
}


/**
 * 下载并打开文件
 * @param {String} fileUrl 文件的完整URL
 * @param {Object} options 配置选项
 * @param {Function} options.onSuccess 下载成功回调
 * @param {Function} options.onFail 下载失败回调
 * @param {Function} options.onComplete 完成回调（无论成功失败）
 * @param {String} options.loadingText 加载提示文字
 * @param {String} options.successText 成功提示文字
 * @param {String} options.failText 失败提示文字
 * @param {Boolean} options.autoOpen 是否自动打开文件
 * @param {String} options.fileName 文件名称，H5环境下使用
 * @returns {Promise<Object>} 下载结果
 */
export function downloadAndOpenFile(fileUrl, options = {}) {
    // 默认选项
    const defaultOptions = {
        onSuccess: null,
        onFail: null,
        onComplete: null,
        loadingText: '文件下载中...',
        successText: '文件已保存',
        failText: '文件下载失败',
        autoOpen: true,
        fileName: ''
    };

    // 合并选项
    const opts = { ...defaultOptions, ...options };

    return new Promise((resolve, reject) => {
        // 验证URL
        if (!fileUrl) {
            const error = new Error('没有可下载的文件');
            if (opts.onFail) opts.onFail(error);
            if (opts.onComplete) opts.onComplete(false);
            reject(error);
            return;
        }

        // 判断当前环境
        // #ifdef H5
        // H5环境下检测是否在微信浏览器中
        const isWechatBrowser = /micromessenger/i.test(navigator.userAgent);

        if (isWechatBrowser) {
            // 微信浏览器环境，直接弹出浏览器下载选项
            try {
                console.log('微信浏览器下载文件:', fileUrl);

                // 直接使用window.open触发下载，让浏览器处理
                window.open(fileUrl, '_blank');

                // 微信浏览器中不显示成功提示，让用户看到浏览器的下载选项
                if (opts.onSuccess) opts.onSuccess({});
                if (opts.onComplete) opts.onComplete(true);
                resolve({});
            } catch (err) {
                console.error('微信浏览器下载文件失败', err);
                toast.show(opts.failText);

                if (opts.onFail) opts.onFail(err);
                if (opts.onComplete) opts.onComplete(false);
                reject(err);
            }
        } else {
            // 普通H5环境使用浏览器原生下载功能
            try {
                uni.showLoading(opts.loadingText);

                // 从URL中提取文件名
                let fileName = opts.fileName || '';
                if (!fileName) {
                    const urlParts = fileUrl.split('/');
                    fileName = urlParts[urlParts.length - 1];
                    // 处理URL编码
                    fileName = decodeURIComponent(fileName);
                }

                // 创建一个隐藏的a标签来下载文件
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = fileUrl;
                a.download = fileName;
                a.target = '_blank';

                // 添加到DOM并触发点击
                document.body.appendChild(a);
                a.click();

                // 清理DOM
                setTimeout(() => {
                    document.body.removeChild(a);
                    uni.hideLoading();

                    if (opts.successText) {
                        toast.show(opts.successText);
                    }

                    if (opts.onSuccess) opts.onSuccess({});
                    if (opts.onComplete) opts.onComplete(true);
                    resolve({});
                }, 100);
            } catch (err) {
                uni.hideLoading();
                console.error('H5环境下载文件失败', err);
                toast.show(opts.failText);

                if (opts.onFail) opts.onFail(err);
                if (opts.onComplete) opts.onComplete(false);
                reject(err);
            }
        }
        // #endif

        // #ifndef H5
        // 非H5环境使用uni API
        uni.showLoading(opts.loadingText);

        // 下载文件
        uni.downloadFile({
            url: fileUrl,
            success: (res) => {
                if (res.statusCode === 200) {
                    // 保存文件
                    uni.saveFile({
                        tempFilePath: res.tempFilePath,
                        success: (saveRes) => {
                            uni.hideLoading();

                            // 显示成功提示
                            if (opts.successText) {
                                toast.show(opts.successText)
                            }

                            // 自动打开文件
                            if (opts.autoOpen) {
                                uni.openDocument({
                                    filePath: saveRes.savedFilePath,
                                    success: () => {
                                        if (opts.onSuccess) opts.onSuccess(saveRes);
                                        if (opts.onComplete) opts.onComplete(true);
                                        resolve(saveRes);
                                    },
                                    fail: (openErr) => {
                                        toast.show('无法打开此类型文件')
                                        if (opts.onSuccess) opts.onSuccess(saveRes);
                                        if (opts.onComplete) opts.onComplete(true);
                                        resolve(saveRes); // 文件保存成功但打开失败，仍然视为成功
                                    }
                                });
                            } else {
                                if (opts.onSuccess) opts.onSuccess(saveRes);
                                if (opts.onComplete) opts.onComplete(true);
                                resolve(saveRes);
                            }
                        },
                        fail: (saveErr) => {
                            uni.hideLoading();

                            toast.show('文件保存失败')

                            if (opts.onFail) opts.onFail(saveErr);
                            if (opts.onComplete) opts.onComplete(false);
                            reject(saveErr);
                        }
                    });
                } else {
                    uni.hideLoading();
                    console.error('文件下载失败', res);

                    toast.show(opts.failText)

                    if (opts.onFail) opts.onFail(res);
                    if (opts.onComplete) opts.onComplete(false);
                    reject(new Error(opts.failText));
                }
            },
            fail: (err) => {
                uni.hideLoading();
                console.error('文件下载失败', err);

                toast.show(opts.failText)

                if (opts.onFail) opts.onFail(err);
                if (opts.onComplete) opts.onComplete(false);
                reject(err);
            }
        });
        // #endif
    });
}
