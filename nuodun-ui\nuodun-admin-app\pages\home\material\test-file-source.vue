<template>
  <view class="container">
    <custom-nav title="文件来源测试" :showLeft="true"></custom-nav>
    
    <view class="test-section">
      <view class="section-title">微信小程序文件选择测试</view>
      
      <button class="test-btn" @tap="testFileSelection">
        测试文件选择功能
      </button>
      
      <view v-if="selectedFiles.length > 0" class="file-list">
        <view class="file-item" v-for="(file, index) in selectedFiles" :key="index">
          <text class="file-name">{{ file.name }}</text>
          <text class="file-size">{{ formatFileSize(file.size) }}</text>
        </view>
      </view>
      
      <view class="log-section">
        <view class="log-title">操作日志：</view>
        <view class="log-item" v-for="(log, index) in logs" :key="index">
          {{ log }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { chooseAndUploadFile, formatFileSize } from '@/common/fileUtils.js'
import CustomNav from '@/components/custom-nav/custom-nav.vue'

const selectedFiles = ref([])
const logs = ref([])

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 10) {
    logs.value.pop()
  }
}

const showFileSourceSelector = () => {
  return new Promise((resolve) => {
    uni.showActionSheet({
      itemList: ['从手机相册选择', '从微信聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          addLog('用户选择：从手机相册选择')
          resolve('album')
        } else if (res.tapIndex === 1) {
          addLog('用户选择：从微信聊天记录选择')
          resolve('chat')
        }
      },
      fail: () => {
        addLog('用户取消选择')
        resolve(null)
      }
    })
  })
}

const testFileSelection = async () => {
  try {
    addLog('开始测试文件选择功能')
    
    // #ifdef MP-WEIXIN
    const sourceType = await showFileSourceSelector()
    if (!sourceType) {
      addLog('测试取消：用户未选择文件来源')
      return
    }
    addLog(`选择的文件来源：${sourceType === 'album' ? '相册' : '聊天记录'}`)
    // #endif
    
    const result = await chooseAndUploadFile({
      count: 5,
      maxSize: 10,
      allowedTypes: ['image', 'document'],
      // #ifdef MP-WEIXIN
      fileSourceType: sourceType,
      // #endif
      autoUpload: false, // 不自动上传，只测试选择功能
      onChoose: (files) => {
        addLog(`选择了 ${files.length} 个文件`)
        return false // 阻止上传
      }
    })
    
    if (result.files && result.files.length > 0) {
      selectedFiles.value = result.files
      addLog(`文件选择成功，共 ${result.files.length} 个文件`)
      result.files.forEach((file, index) => {
        addLog(`文件${index + 1}: ${file.name} (${formatFileSize(file.size)})`)
      })
    } else {
      addLog('没有选择到文件')
    }
    
  } catch (error) {
    addLog(`文件选择失败：${error.message}`)
    console.error('文件选择测试失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.test-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #1890ff 0%, #4dabff 100%);
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 30rpx;
  border: none;
}

.file-list {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.file-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.file-size {
  font-size: 24rpx;
  color: #666;
}

.log-section {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.log-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  
  &:last-child {
    border-bottom: none;
  }
}
</style>
