# 微信小程序文件选择功能说明

## 功能概述

为了解决微信小程序中文件上传只能从聊天记录选择的问题，我们添加了文件来源选择功能，让用户可以选择从相册或聊天记录中获取文件。

## 主要修改

### 1. 文件工具类修改 (`common/fileUtils.js`)

- 添加了 `fileSourceType` 参数，支持 `'album'` 和 `'chat'` 两种文件来源
- 修改了小程序环境的文件选择逻辑：
  - 从相册选择：只能选择图片（微信小程序API限制）
  - 从聊天记录选择：支持所有文件类型

### 2. 材料上传页面修改 (`pages/home/<USER>/materialUpload.vue`)

- 添加了 `showFileSourceSelector()` 函数，在微信小程序中显示文件来源选择器
- 修改了 `chooseFiles()` 函数，在微信小程序环境下先让用户选择文件来源
- 更新了上传按钮的提示文本

### 3. 测试页面 (`pages/home/<USER>/test-file-source.vue`)

- 创建了专门的测试页面，用于验证文件选择功能
- 包含详细的操作日志，便于调试

## 使用方式

### 在微信小程序中

1. 用户点击"上传文件"按钮
2. 系统弹出选择器：
   - "从手机相册选择（仅图片）"
   - "从微信聊天记录选择（所有文件）"
3. 用户选择后，系统调用对应的API进行文件选择

### 在其他平台

- H5、APP等其他平台的行为保持不变
- 继续使用原有的文件选择逻辑

## 技术细节

### 文件来源类型

```javascript
// 从相册选择（仅图片）
fileSourceType: 'album'

// 从聊天记录选择（所有文件）
fileSourceType: 'chat'
```

### API调用示例

```javascript
const result = await chooseAndUploadFile({
  count: 10,
  maxSize: 10,
  allowedTypes: ['image', 'document'],
  // #ifdef MP-WEIXIN
  fileSourceType: sourceType, // 'album' 或 'chat'
  // #endif
  uploadData: {
    fileBizType: 'material_upload',
    materialOptId: materialOptId
  }
})
```

### 微信小程序API限制

- `uni.chooseImage()`: 只能选择图片，支持从相册或拍照
- `uni.chooseMessageFile()`: 可以选择所有文件类型，但只能从聊天记录中选择

## 注意事项

1. **相册选择限制**: 从相册只能选择图片，这是微信小程序API的限制
2. **文件类型**: 如果需要上传文档等非图片文件，必须选择"从聊天记录选择"
3. **用户体验**: 选择器会明确标注每种选择方式支持的文件类型
4. **兼容性**: 修改只影响微信小程序，其他平台保持原有行为

## 测试建议

1. 在微信开发者工具中测试文件选择功能
2. 在真机上测试相册选择和聊天记录选择
3. 验证不同文件类型的上传是否正常
4. 确认用户界面提示是否清晰

## 后续优化

1. 可以考虑根据材料类型自动选择合适的文件来源
2. 添加更详细的用户引导说明
3. 优化错误提示和用户反馈
